# RS485 Driver API Design Document

## 1. Introduction

This document outlines the API design for the RS485 driver software that enables communication between a PC and AI-SLDAP devices over RS485. The driver is implemented using Windows Driver Kit (WDK) with User-Mode Driver Framework (UMDF) and interfaces with FPGA-based slave devices using the ZES protocol.

### 1.1 Executive Summary

- **Purpose**: Create a robust Windows application for RS485 communication with AI-SLDAP devices
- **Key Features**: Highly abstracted API with five distinct categories for device control
- **Implementation**: Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality and advanced buffer management
- **Final Deliverable**: Single executable application (.exe) that provides complete RS485 communication solution

### 1.2 Scope

This document focuses on the implementation of a comprehensive RS485 driver that communicates with AI-SLDAP devices using Windows Driver Kit (WDK). The design is based on the ZES protocol with enhancements to address specific project requirements and Windows driver architecture.

**Hardware Requirements:**
- USB-RS485-WE-1800-BT FTDI converter for PC-side interface
- ZM-AISL-01 (FPGA) board as slave device
- Single twisted pair RS485 cable in BUS topology
- Maximum 32 devices supported on the bus (30 slaves + 1 master)

**Driver Architecture:**
The implementation follows the OSI model adapted for ZM-AISL-01 protocol stack:
- **Physical Layer**: EIA/TIA-485 (RS485) via USB-RS485 converter
- **Data Link Layer**: ZES proprietary protocol implemented as Windows User-Mode Driver Framework (UMDF 2) with driver-managed buffers
- **Application Layer**: User Application (via ZES driver API)

### 1.3 API Categories

The API design follows the ZES driver API classification with five key components, allowing users to interact with the system without understanding the underlying protocol details:

#### 1.3.1 ZES Driver API Classification and Function Code Correspondence

**Critical Design Principle: Function Code to API Category Mapping**

The five API categories directly correspond to the ZES protocol function codes in the ID byte. This correspondence is fundamental to the driver's operation:

| Function Code | Binary | Description | API Category | Used By |
|:-------------:|:------:|:------------|:-------------|:--------|
| **0b111** | Assign data | **Master Broadcasting API** + **Master Assign Data API** | Master |
| **0b110** | Request data | **Master Request API** | Master |
| **0b010** | Response to Assign | **Slave Response API** (for assign acknowledgments) | Slave |
| **0b001** | Response to Request | **Slave Response API** (for data responses) | Slave |
| **0b000** | Re-send request | **Error Handle API** (automatic retry mechanism) | Both |

**Function Code Processing Logic:**
The driver automatically determines which API category to use based on the function code in the ID byte:
- **0b111 (Assign)**: Routes to Broadcasting API (S-series) or Assign Data API (U/W-series)
- **0b110 (Request)**: Routes to Master Request API (A-series)
- **0b010/0b001 (Responses)**: Routes to Slave Response API with appropriate handling
- **0b000 (Re-send)**: Routes to Error Handle API for automatic retry processing

The API is organized in the following logical order:

1. **Error Handle API**: `getErrorString(error)` + **Management APIs**
   - **Function Code**: 0b000 (Re-send request) - handles automatic retry mechanism
   - **FTDI-Style Management**: Includes port management functions similar to FTDI RS485 drivers
   - **Buffer Management**: Provides buffer status checking before data transmission
   - **Error Categorization**: Handles COM port errors inherited from FTDI's driver
   - **Management Functions**: `openPort()`, `closePort()`, `isPortOpen()`, `getBufferStatus()`, `clearBuffer()`
   - **Buffer Flag Checking**: Mandatory buffer status verification before sending data
   - **Retry Logic**: Intelligent error recovery with transient vs. permanent error categorization

2. **Master Broadcasting API**: `configureSystemSettings(commandKey, value)` for S-series commands only
   - **Function Code**: 0b111 (Assign data) - specifically for system-level broadcasting
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **Hardware Requirement**: Only one slave device connected to prevent broadcast conflicts
   - **Address Assignment**: Sets the slave address used by subsequent U-series commands
   - **Runtime Validation**: Detects multiple slaves when only one is expected
   - **Acknowledgment**: Mandatory acknowledgment mechanism for reliability (function code 0b010)

3. **Master Assign Data API**: `configureUserSettings(commandKey, value)` + `modelDataOperation(slaveAddress, address, data, isWrite, length)`
   - **Function Code**: 0b111 (Assign data) - for targeted device configuration
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **U-series Commands**: User configuration parameters using address from S001 command
   - **W-series Commands**: AI model weights and bias data stored in FRAM memory
   - **Address Resolution**: Uses slave address previously set by S001 or default address
   - **Acknowledgment**: Expects acknowledgment responses (function code 0b010)

4. **Master Request API**: `requestData(slaveAddress, dataKey, responseData)`
   - **Function Code**: 0b110 (Request data) - for information queries
   - **Buffer Check**: Automatically checks uplink buffer flag before transmission
   - **Non-blocking Design**: Returns quickly, data retrieved later via Slave Response API
   - **A-series Commands**: Application-related data queries and status requests
   - **Response Handling**: Expects data responses (function code 0b001)
   - **Airborne Compatibility**: Critical for multi-task airborne environments

5. **Slave Response API**: `receiveSlaveResponse(slaveAddress, responseData, waitForData, timeout)`
   - **Function Codes**: 0b010 (Response to Assign) and 0b001 (Response to Request)
   - **Buffer Management**: Automatically checks downlink buffer flag before storing data
   - **FIFO Processing**: Maintains strict First-In-First-Out ordering for response data
   - **Response Types**: Handles both acknowledgments (0b010) and data responses (0b001)
   - **Buffer Overflow**: Configurable overflow policies when downlink buffer is full
   - **Data Ready Notification**: Callback mechanism for asynchronous data availability

This comprehensive API design allows users to focus on functional requirements without having to understand the underlying protocol details. The function code correspondence ensures that each API call is automatically routed to the correct protocol handling mechanism.

#### 1.3.2 Management APIs and Buffer Control

**FTDI-Style Management Functions**

Following industry-standard serial port interface patterns, the RS485 driver includes comprehensive management APIs similar to FTDI RS485 drivers:

**Port Management APIs:**
```cpp
// Basic port operations (similar to FTDI FT_Open, FT_Close)
RS485Error openPort(const std::string& portName);
RS485Error closePort();
bool isPortOpen() const;
RS485Error getPortInfo(PortInfo& info);

// Device enumeration (similar to FTDI FT_ListDevices)
static RS485Error enumerateDevices(std::vector<DeviceInfo>& deviceList);
static RS485Error detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);
```

**Buffer Management APIs (Critical for Data Integrity):**
```cpp
// Buffer status checking - MANDATORY before data transmission
RS485Error getBufferStatus(BufferStatus& status);
RS485Error checkUplinkBufferFlag(bool& isFull);
RS485Error checkDownlinkBufferFlag(bool& isFull);

// Buffer control operations
RS485Error clearBuffer(BufferType bufferType = BufferType::BOTH);
RS485Error setBufferOverflowPolicy(BufferOverflowPolicy policy);
RS485Error getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

// Buffer monitoring and notifications
RS485Error setBufferThreshold(uint32_t thresholdPercent);
void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);
```

**Hardware Status APIs (Similar to FTDI FT_GetStatus):**
```cpp
// Hardware and communication status
RS485Error getHardwareStatus(HardwareStatus& status);
RS485Error getPerformanceMetrics(PerformanceMetrics& metrics);
RS485Error getBaudRate(uint32_t& currentBaudRate);
RS485Error getLineStatus(LineStatus& status);
```

**Critical Buffer Flag Checking Mechanism:**

Every data transmission operation automatically performs buffer flag checking:

1. **Before Sending Data**: Driver checks uplink buffer flag to ensure space is available
2. **Before Storing Received Data**: Driver checks downlink buffer flag to prevent overflow
3. **FIFO Guarantee**: Strict First-In-First-Out ordering maintained for both directions
4. **Overflow Handling**: Configurable policies when buffers reach capacity

**Example Buffer Check Implementation:**
```cpp
// Internal implementation - automatically called before each transmission
RS485Error checkBufferBeforeTransmission() {
    BufferStatus status;
    RS485Error result = getBufferStatus(status);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    if (status.isUplinkFull) {
        // Apply overflow policy
        switch (m_bufferOverflowPolicy) {
            case BufferOverflowPolicy::DISCARD_OLDEST:
                clearOldestUplinkFrame();
                break;
            case BufferOverflowPolicy::DISCARD_NEWEST:
                return RS485Error::INSUFFICIENT_BUFFER;
            case BufferOverflowPolicy::TRIGGER_ERROR:
                return RS485Error::INSUFFICIENT_BUFFER;
        }
    }

    return RS485Error::SUCCESS;
}
```

This management API design follows industry-standard serial port communication patterns while providing the advanced buffer management required for reliable RS485 communication.

#### 1.3.3 Command Addressing Mechanism

**Important Note on U-series Command Addressing:**

The RS485 driver implements an implicit addressing mechanism for U-series commands:

1. When using `configureUserSettings(commandKey, value)` for U-series commands, the target slave address is determined by:
   - The slave address previously set by an S001 command via `configureSystemSettings`
   - The default slave address (0x00) if no S001 command has been executed

2. This design means that:
   - You should typically set the slave address using S001 before sending U-series commands
   - All U-series commands will be sent to the same slave address until a new S001 command is executed
   - If you need to configure multiple slaves, you must change the slave address using S001 between each set of U-series commands

3. Sequence for proper addressing:
   - First use `configureSystemSettings(0x53303031, slaveAddress)` to set the target slave address
   - Then use `configureUserSettings(0x55xxxxxx, value)` for U-series commands to that slave
   - Repeat this sequence for each slave device that needs configuration

### 1.4 Command Categories and Definitions

The ZES protocol defines several categories of commands that can be exchanged between the master (PC) and slave (FPGA) devices:

#### 1.4.1 System Configuration Commands (S-series)

| Command | Description | Value Range | ASCII Key |
|---------|-------------|-------------|-----------|
| **S001** | Set RS485 slave address | 1-31 | 0x53303031 |
| **S002** | Set baud rate | 9600, 19200, 38400, 57600, 115200 | 0x53303032 |

**Important Notes on S-series Commands:**
- **S001 (Address Assignment)**: All AI-SLDAP boards are initialized with default address 0x00 during manufacturing. To assign a user-designated address, connect the board alone (without other slaves) and use broadcasting to address 0x00. The FPGA will write the new address to FRAM for persistence across power cycles.
  - **Conflict Prevention**: To prevent address conflicts, ensure only one slave device is connected to the RS485 bus during the S001 address assignment process. If multiple slaves respond to the S001 broadcast frame, the master should abort the assignment and alert the user to isolate a single slave.
- **S002 (Baud Rate Assignment)**: Slave devices use default baud rate 9600 upon power-on before assignment. After receiving S002 command, the slave switches to the new baud rate and sends an acknowledgment frame at the new baud rate. The master waits for this acknowledgment before continuing communication at the new baud rate.
  - **Confirmation Mechanism**: The slave acknowledges the baud rate change by sending a response frame (function code 0b010) at the newly assigned baud rate, which the master must receive before proceeding with further communication.

#### 1.4.2 User Configuration Commands (U-series)

| Command | Description | Value Range | ASCII Key |
|---------|-------------|-------------|-----------|
| **U001** | Set SEL detection threshold | 40-500 milliampere | 0x55303031 |
| **U002** | Set SEL maximum amplitude threshold | 1000-2000 milliampere | 0x55303032 |
| **U003** | Set number of SEL detections before power cycle | 1-5 | 0x55303033 |
| **U004** | Set power cycle duration | 200, 400, 600, 800, or 1000 milliseconds | 0x55303034 |
| **U005** | Enable/disable GPIO input functions | See GPIO Value Packing below | 0x55303035 |
| **U006** | Enable/disable GPIO output functions | See GPIO Value Packing below | 0x55303036 |

**Configuration Persistence:**
All U-series configuration settings are automatically saved to the FPGA board's FRAM (Ferroelectric Random Access Memory) upon successful execution. This ensures that:
- **Non-volatile Storage**: Configuration settings persist across power cycles and system reboots
- **One-time Setup**: Users only need to configure the device once; settings are retained permanently
- **Immediate Effect**: Changes take effect immediately and are simultaneously saved to FRAM
- **Reliability**: FRAM technology provides high endurance and data retention without requiring external power

**Reading Current Configuration:**
To retrieve the current configuration values stored in FRAM, use the A005 command (see Section 1.4.3 Application Data Commands).

**GPIO Value Packing for U005/U006:**
The `value` parameter for GPIO commands uses the following bit layout:
```cpp
// GPIO Value Packing Format:
// Lower 8 bits: Channel ID (0 or 1)
// Next 8 bits: Enable/Disable flag (0 = disable, 1 = enable)
// Upper 48 bits: Reserved (set to 0)

uint64_t value = (channel_id & 0xFF) | ((enable_flag & 0xFF) << 8);

// Examples:
// Enable GPIO input channel 0:  value = 0 | (1 << 8) = 0x0100
// Disable GPIO input channel 1: value = 1 | (0 << 8) = 0x0001
// Enable GPIO output channel 1:  value = 1 | (1 << 8) = 0x0101
```

#### 1.4.3 Application Data Commands (A-series)

| Command | Description | Response Data | ASCII Key |
|---------|-------------|---------------|-----------|
| **A001** | Request SEL event log | JSON structure with event records | 0x41303031 |
| **A002** | Request device status | Status flags (16-bit) | 0x41303032 |
| **A003** | Request firmware version | Version string | 0x41303033 |
| **A004** | Request system statistics | JSON structure with statistics | 0x41303034 |
| **A005** | Request current configuration | JSON structure with all current settings | 0x41303035 |

**Example A001 Response Format:**
```json
{
  "events": [
    {
      "id": 1,
      "timestamp": "2024-12-15T14:30:22Z",
      "type": "SEL_DETECTED",
      "current": 320,
      "action": "POWER_CYCLE"
    },
    {
      "id": 2,
      "timestamp": "2024-12-15T18:45:10Z",
      "type": "SEL_DETECTED",
      "current": 450,
      "action": "POWER_CYCLE"
    }
  ],
  "total_count": 2
}
```

**Example A004 Response Format:**
```json
{
  "statistics": {
    "total_runtime_hours": 720,
    "power_cycles": {
      "total": 15,
      "last_24h": 2,
      "last_7d": 8,
      "last_30d": 15
    },
    "sel_events": {
      "total": 12,
      "last_24h": 1,
      "last_7d": 6,
      "last_30d": 12
    },
    "average_current": 180
  },
  "timestamp": "2024-12-16T09:30:00Z"
}
```

**Example A005 Response Format:**
```json
{
  "configuration": {
    "system_settings": {
      "slave_address": 5,
      "baud_rate": 115200
    },
    "user_settings": {
      "sel_detection_threshold": 250,
      "sel_max_amplitude_threshold": 1500,
      "sel_detection_count": 3,
      "power_cycle_duration": 600,
      "gpio_input_channels": {
        "channel_0": true,
        "channel_1": false
      },
      "gpio_output_channels": {
        "channel_0": false,
        "channel_1": true
      }
    },
    "last_updated": "2024-12-16T10:15:30Z",
    "fram_status": "healthy"
  }
}
```

#### 1.4.4 Weight and Bias Data Commands (W-series)

| Operation | Description | Parameters | ASCII Key |
|-----------|-------------|------------|-----------|
| **W001** | Write model data to FRAM | Memory address, data bytes | 0x57303031 |
| **W002** | Read model data from FRAM | Memory address, length | 0x57303032 |

### 1.5 Implementation Approach

- **PC Side**: Single Windows executable application incorporating Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality
- **FPGA Side**: VHDL/Verilog implementation of the slave device functionality (ZM-AISL-01 boards)
- **Data Link Layer Implementation**: Native Windows driver using WDK implementing ZES protocol with embedded FTDI VCP driver capabilities
- **Hardware Interface**: Built-in support for USB-RS485-WE-1800-BT FTDI converter without requiring separate driver installation
- **Buffer Management**: Driver-managed frame buffers (5 uplink + 10 downlink frames, 16 bytes each) with intelligent overflow handling
- **API Design Philosophy**: High abstraction level allowing future functionality to be added without modifying the core application
- **Deployment Model**: Single executable deployment eliminates driver installation complexity while maintaining enterprise-grade reliability

### 1.6 Windows Driver Kit (WDK) Integration and Application Architecture

**Application Framework Selection:**
The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) within a single executable, providing:

1. **Device Type Compatibility**: RS485 communication devices are well-suited for user-mode driver integration
2. **Simplified Deployment**: Single executable eliminates complex driver installation procedures
3. **System Stability**: User-mode implementation cannot crash the system, providing better overall stability
4. **Framework Benefits**: UMDF handles Plug and Play (PnP) and power management automatically
5. **FTDI Integration**: Built-in FTDI VCP driver functionality eliminates dependency on external driver installations

**Key WDK Components Integrated:**
- **UMDF 2.0**: User-Mode Driver Framework integrated within the application
- **WDF I/O Queues**: For managing communication requests and asynchronous operations
- **WDF Device Objects**: For representing the RS485 device interface
- **WDF Memory Objects**: For efficient buffer management and data transfer
- **FTDI VCP Integration**: Embedded FTDI Virtual COM Port driver functionality

**Application Deployment Model:**
- **Single Executable**: Complete solution packaged as RS485_Communication_Application.exe
- **No Driver Installation**: Application handles all driver functionality internally
- **Plug and Play Support**: Automatic detection and configuration of USB-RS485 converters
- **Windows Compatibility**: Full compatibility with Windows 10/11 without requiring administrator privileges for driver installation
- **Development Environment**: Visual Studio integration with WDK for development and testing

## 2. Architecture Overview

### 2.0 Integrated Application Architecture

The RS485 communication application integrates Windows Driver Kit (WDK) User-Mode Driver Framework (UMDF 2) with embedded FTDI VCP driver functionality, providing a complete RS485 communication solution in a single executable.

**Integrated Application Architecture:**
```
┌─────────────────────────────────────┐
│   RS485_Communication_Application   │
│              (.exe)                 │
├─────────────────────────────────────┤
│        High-Level API Layer         │
│  • configureSystemSettings()        │
│  • configureUserSettings()          │
│  • requestData()                    │
│  • receiveSlaveResponse()           │
│  • modelDataOperation()             │
├─────────────────────────────────────┤
│   Integrated UMDF 2.0 Framework     │
│  ┌─────────────────────────────────┐│
│  │    Driver-Managed Buffers       ││
│  │  ┌─────────────┬─────────────┐  ││
│  │  │ Uplink (5)  │Downlink(10) │  ││
│  │  │ 60 bytes    │ 120 bytes   │  ││
│  │  │(payloadonly)│(payloadonly)│  ││
│  │  └─────────────┴─────────────┘  ││
│  │                                 ││
│  │   ZES Protocol Processing       ││
│  │   • Frame packing/unpacking     ││
│  │   • CRC8 calculation            ││
│  │   • Function code routing       ││
│  │   • Error handling & retry      ││
│  └─────────────────────────────────┘│
├─────────────────────────────────────┤
│    Embedded FTDI VCP Functionality  │
│    (Integrated USB-Serial Driver)   │
├─────────────────────────────────────┤
│      Windows USB Stack              │
├─────────────────────────────────────┤
│   USB-RS485-WE-1800-BT Converter    │
├─────────────────────────────────────┤
│         RS485 Bus                   │
└─────────────────────────────────────┘
```

**Application Component Roles:**

1. **Embedded FTDI VCP Functionality** (Lower Layer):

   * **Role**: Integrated USB-to-Serial Driver – Communicates directly with the USB-RS485 hardware
   * **Responsibility**: USB-to-serial conversion and basic serial communication functions
   * **Implementation**: Built-in FTDI VCP driver functionality embedded within the application
   * **Interface**: Internal serial port interface (CreateFile, ReadFile, WriteFile) managed by the application

2. **ZES Protocol Processing Layer** (Upper Layer):

   * **Role**: Protocol Handler – Implements ZES proprietary data link layer protocol
   * **Responsibility**:

     * RS485 protocol handling (ZES protocol as specified in guidance document)
     * Intelligent buffer management (5 uplink + 10 downlink frames)
     * Frame packing/unpacking and CRC8 checksum verification
     * Function code routing and API category management
     * Advanced API interface (5 main categories of APIs)
   * **Framework**: UMDF 2.0 (User-Mode Driver Framework) integrated within application
   * **Interface**: High-level API functions for user applications

### 2.1 Driver-Managed Buffer System

**Buffer Architecture:**
The driver implements a sophisticated buffer management system to handle the asynchronous nature of RS485 communication:

**Uplink Buffer (PC to Device):**
- **Capacity**: 5 frames × 12 bytes = 60 bytes total (payload data only)
- **Purpose**: Queue outgoing commands and data from PC to slave devices
- **Management**: FIFO (First-In-First-Out) queue managed by the driver
- **Overflow Policy**: Configurable (discard oldest, discard newest, or return error)
- **Buffer Flag**: Monitored to prevent overflow - checked before each data transmission

**Downlink Buffer (Device to PC):**
- **Capacity**: 10 frames × 12 bytes = 120 bytes total (payload data only)
- **Purpose**: Store incoming responses and data from slave devices
- **Management**: FIFO queue with per-slave address organization
- **Data Ready Notification**: Callback mechanism to notify applications of available data
- **Buffer Flag**: Monitored to prevent overflow - checked before storing received data

**Frame Structure (16 bytes per frame):**
```cpp
struct RS485Frame {
    uint8_t header;        // 0xAA (1 byte)
    uint8_t id_byte;       // Function code + device address (1 byte)
    uint8_t payload[12];   // Key (4 bytes) + Value (8 bytes) - CORE PROTOCOL DATA
    uint8_t crc8;          // CRC checksum (1 byte)
    uint8_t trailer;       // 0x0D (1 byte)
};  // Total: 16 bytes
```

**Critical Protocol Design Note:**
The **12-byte payload** is the core of the entire RS485 communication protocol. This payload contains the essential Key-Value pair data that enables all communication between PC and slave devices. The driver's buffer management system is specifically designed around efficiently handling these 12-byte payload segments, as they contain all the meaningful data for:
- System configuration commands (S-series)
- User configuration commands (U-series)
- Application data requests (A-series)
- AI model weight/bias data (W-series)

**Buffer Management Features:**
- **Thread-Safe Operations**: All buffer operations are protected by appropriate synchronization mechanisms
- **Memory Efficiency**: Fixed-size buffers eliminate dynamic memory allocation overhead
- **Real-Time Performance**: Optimized for low-latency communication in airborne environments
- **Non-Blocking Design**: Driver operations never block user threads
- **Asynchronous Processing**: Frame processing occurs in driver-managed work items and DPCs
- **Buffer Status Monitoring**: Applications can query buffer usage and availability
- **Buffer Overflow Prevention**:
  - **Pre-transmission Check**: Before sending data, driver checks uplink buffer flag to ensure space is available
  - **Pre-storage Check**: Before storing received data, driver checks downlink buffer flag to prevent overflow
  - **FIFO Guarantee**: Strict First-In-First-Out ordering maintained for both PC User side and driver side
  - **Overflow Handling**: Configurable policies when buffers reach capacity (discard oldest/newest or trigger error)

### 2.3 Non-Blocking Driver Architecture

**Critical Design Principle: Never Block User Threads**

The RS485 Filter Driver is designed with a fundamental principle that **driver operations must never block user application threads**. This is essential for:

1. **Airborne Environment Requirements**: Real-time systems cannot afford thread blocking
2. **System Responsiveness**: User applications remain responsive during RS485 operations
3. **Scalability**: Multiple applications can interact with the driver simultaneously
4. **Windows Driver Best Practices**: Follows Microsoft's guidelines for driver development

**Implementation Strategy:**

```cpp
// Driver uses asynchronous I/O completion model
class RS485FilterDriver {
private:
    // Work items for asynchronous processing
    IWDFWorkItem* m_pFrameProcessingWorkItem;
    IWDFWorkItem* m_pTransmitWorkItem;

    // I/O completion callbacks
    IWDFRequestCompletionParams* m_pCompletionParams;

    // Driver-managed thread pool for frame processing
    WDFWORKITEM m_frameProcessorWorkItems[4];  // 4 worker threads

public:
    // All API calls return immediately with STATUS_PENDING or completion status
    NTSTATUS ProcessIOCTLRequest(IWDFIoRequest* pRequest);

    // Asynchronous completion callbacks
    void OnFrameReceived(IWDFIoRequest* pRequest);
    void OnTransmitComplete(IWDFIoRequest* pRequest);
};
```

### 2.4 Frame Processing Architecture

**Asynchronous Frame Processing Pipeline**

The driver implements a sophisticated frame processing pipeline that operates independently of user threads. The core focus is on efficiently extracting and managing the **12-byte payload data** which contains all meaningful communication information:

**1. Receive Path (Device to PC):**
```
FTDI VCP Driver → Filter Driver → Frame Parser → Payload Extractor → Buffer Flag Check → Buffer Manager → User Application
     ↓              ↓              ↓              ↓                ↓                ↓              ↓
  Hardware IRQ → Work Item → DPC Context → 12-byte payload → Check downlink flag → Ring Buffer → IOCTL Completion
```

**2. Transmit Path (PC to Device):**
```
User Application → Buffer Flag Check → Buffer Manager → Frame Builder → Filter Driver → FTDI VCP Driver
     ↓              ↓                ↓              ↓              ↓              ↓
  IOCTL Request → Check uplink flag → Ring Buffer → Work Item → DPC Context → Hardware
```

**Buffer Flag Check Process:**
- **Before Transmission**: Driver checks uplink buffer flag to ensure space is available for the 12-byte payload
- **Before Storage**: Driver checks downlink buffer flag to ensure space is available for incoming 12-byte payload
- **FIFO Enforcement**: Both PC User side and driver side maintain strict FIFO ordering for payload data
- **Overflow Prevention**: If buffer is full, driver applies configured overflow policy (discard oldest/newest or return error)

**Frame Processing State Machine with Function Code Routing:**
```cpp
enum class FrameState {
    WAITING_HEADER,     // Looking for 0xAA
    READING_ID,         // Reading ID byte (contains function code)
    READING_PAYLOAD,    // Reading 12-byte payload
    READING_CRC,        // Reading CRC8
    READING_TRAILER,    // Looking for 0x0D
    FRAME_COMPLETE,     // Frame ready for processing
    FRAME_ERROR         // CRC or format error
};

class FrameProcessor {
private:
    FrameState m_currentState;
    uint8_t m_frameBuffer[16];
    size_t m_bytesReceived;

public:
    // Called from DPC context - never blocks
    FrameProcessResult ProcessIncomingByte(uint8_t byte);

    // Called from work item - performs function code routing and buffer management
    void ProcessCompleteFrame(const RS485Frame& frame);

    // Function code processing and API routing
    void RouteFrameToAPICategory(const RS485Frame& frame);

private:
    // Function code extraction and validation
    uint8_t ExtractFunctionCode(uint8_t idByte) { return (idByte >> 5) & 0x07; }
    uint8_t ExtractDeviceAddress(uint8_t idByte) { return idByte & 0x1F; }

    // API category routing based on function code
    void ProcessAssignDataFrame(const RS485Frame& frame);      // 0b111
    void ProcessRequestDataFrame(const RS485Frame& frame);     // 0b110
    void ProcessResponseAssignFrame(const RS485Frame& frame);  // 0b010
    void ProcessResponseRequestFrame(const RS485Frame& frame); // 0b001
    void ProcessResendRequestFrame(const RS485Frame& frame);   // 0b000

    // Buffer management with flag checking
    bool CheckAndManageUplinkBuffer(const uint8_t* payload);
    bool CheckAndManageDownlinkBuffer(const uint8_t* payload, uint8_t deviceAddress);
};
```

**Function Code Processing Logic:**
```cpp
void FrameProcessor::ProcessCompleteFrame(const RS485Frame& frame) {
    // Extract function code from ID byte
    uint8_t functionCode = ExtractFunctionCode(frame.id_byte);
    uint8_t deviceAddress = ExtractDeviceAddress(frame.id_byte);

    // Route to appropriate API category based on function code
    switch (functionCode) {
        case 0b111:  // Assign data (Master use)
            // Routes to Master Broadcasting API or Master Assign Data API
            ProcessAssignDataFrame(frame);
            break;

        case 0b110:  // Request data (Master use)
            // Routes to Master Request API
            ProcessRequestDataFrame(frame);
            break;

        case 0b010:  // Response to Assign (Slave use)
            // Routes to Slave Response API - acknowledgment handling
            if (CheckAndManageDownlinkBuffer(frame.payload, deviceAddress)) {
                ProcessResponseAssignFrame(frame);
            }
            break;

        case 0b001:  // Response to Request (Slave use)
            // Routes to Slave Response API - data response handling
            if (CheckAndManageDownlinkBuffer(frame.payload, deviceAddress)) {
                ProcessResponseRequestFrame(frame);
            }
            break;

        case 0b000:  // Re-send request (Both use)
            // Routes to Error Handle API - automatic retry mechanism
            ProcessResendRequestFrame(frame);
            break;

        default:
            // Invalid function code - log error
            LogError(RS485Error::INVALID_FUNCTION_CODE, frame);
            break;
    }
}
```

### 2.2 RS485 Electrical Interface

The RS485 interface used in this project follows the TIA/EIA-485 standard:

- **Differential Signaling**: Uses a balanced line with two conductors (A and B)
- **Voltage Levels**: Signal levels are typically ±2V to ±6V
- **Noise Immunity**: High common-mode rejection ratio due to differential signaling
- **Distance**: Supports communication up to 1200 meters (4000 feet)
- **Speed**: Supports data rates up to 10 Mbps (depending on cable length)
- **Multi-drop**: Supports up to 32 unit loads on a single bus
- **Half-duplex**: Communication in one direction at a time

The USB-RS485-WE-1800-BT FTDI converter is used on the PC side to interface with the RS485 bus. Our RS485 Filter Driver sits above the FTDI VCP Function Driver, leveraging the existing FTDI driver infrastructure while adding RS485 protocol intelligence and advanced buffer management.

## 3. API Specification

### 3.0 Communication Flow and Protocol Overview

The following sequence diagram illustrates the typical communication flow between master (PC) and slave (FPGA) devices:

```
Master (PC)                    Slave (FPGA)
     |                              |
     |  S001 Broadcast (Address=0)  |
     |----------------------------->|
     |                              | (Process address assignment)
     |                              | (Write to FRAM)
     |  Acknowledgment (Response2)  |
     |<-----------------------------|
     |                              |
     |  U001 Command (Address=5)    |
     |----------------------------->|
     |                              | (Configure SEL threshold)
     |  Acknowledgment (Response2)  |
     |<-----------------------------|
     |                              |
     |  A001 Request (Address=5)    |
     |----------------------------->|
     |                              | (Prepare event log data)
     |  Data Response (Response1)   |
     |<-----------------------------|
     |                              |

Error Handling Flow:
     |  Command with CRC Error      |
     |----------------------------->|
     |                              | (Detect CRC error)
     |  Re-send Request (Code=000)  |
     |<-----------------------------|
     |  Retry Command (up to 3x)    |
     |----------------------------->|
     |  Acknowledgment/Data         |
     |<-----------------------------|
```

**Key Communication Principles:**
1. Master initiates all communication
2. Slaves respond within 100ms response window
3. Broadcast commands (S-series) require single slave connection
4. CRC errors trigger automatic retry mechanism (up to 3 attempts)
5. Timeout errors may indicate hardware failure or broken link

### 3.1 Operation Rules for Avoiding Data Collisions

In half-duplex master-slave bus topology communication, the following rules must be observed to avoid data collisions:

1. **Master Responsibility**: The master (PC) initiates all communication, controls the bus data flow, and manages bus status (including idle time). Slaves can only send data frames as responses to master requests/queries and only respond to requests addressed to their own node.

2. **Response Window Time**: Slaves must respond to master requests within the response window time (100ms as defined in ZES protocol). No data should be sent when the response window time has elapsed.

3. **Transmitter Control**: As a slave, only enable the transmitter when ready to send data, and disable it immediately after sending the stop bit. This ensures the receiver is ready to listen to the RS485 bus.

4. **Broadcasting Rules**: Only the master can send broadcasting messages (to address 0x00). No response is required from slaves to broadcasting messages except for slave address assignment messages (S001).

5. **Error Recovery**: If the master does not receive a response from a designated slave, it is the master's discretion to send the request again. This may indicate a broken link or slave node hardware failure.

6. **Bus Idle State**: When the serial bus is idle (no driver enabled), the RS485 transceiver IC guarantees the receiver reads logic high, ensuring proper detection of the start bit's high-to-low transition by the UART controller.

### 3.2 UART Frame Format

The UART communication uses the following frame format:

| Start Bit | Data Bits (0-7) | Parity Bit | Stop Bit |
|:---------:|:---------------:|:----------:|:--------:|
| 1 bit     | 8 bits          | None       | 1 bit    |

Total: 10 bits per byte (1 start + 8 data + 0 parity + 1 stop)

This is commonly referred to as "8N1" format (8 data bits, No parity, 1 stop bit).

### 3.3 ZES Protocol Frame Format

The ZES protocol uses a 16-byte frame format as follows:

| Header | ID Byte | Data Payload (12 bytes) | CRC8 | Trailer |
|:------:|:-------:|:-----------------------:|:----:|:-------:|
| 0xAA   | 1 byte  | Key (4 bytes) + Value (8 bytes) | 1 byte | 0x0D |

**Frame Structure Details:**
- **Header (0xAA)**: Fixed start byte for frame synchronization
- **ID Byte**: Contains function code (3 bits) + device address (5 bits)
- **Data Payload**: 12 bytes containing Key-Value pair in JSON format
  - Key: 4 bytes (ASCII command identifier)
  - Value: 8 bytes (binary data value)
- **CRC8**: Error detection using polynomial $0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1$
- **Trailer (0x0D)**: Fixed end byte for frame termination

**CRC8 Implementation:**
- Covers 13 bytes from ID byte to data payload (excluding header byte)
- Both master and slave must calculate and verify CRC
- On CRC error, a re-send request frame is immediately transmitted
- If header byte is corrupted, frame is automatically dropped (timeout-based recovery)

**CRC8 Calculation Example:**
For a frame with ID `0xE1` and payload `0x57 0x30 0x30 0x31 0x00 0x00 0x00 0x05 0x00 0x00 0x00 0x00`:
1. Input data: 13 bytes (ID + 12-byte payload)
2. Polynomial: $0 x 97=x^{8}+x^{5}+x^{3}+x^{2}+x+1$
3. Initial value: 0x00
4. The CRC8 calculation processes each byte sequentially using the polynomial
5. Result: CRC byte to be placed in the frame

**Error Handling and Retry Mechanism:**
- Upon detecting a CRC error, the receiver (master or slave) sends a re-send request frame (function code 0b000)
- The sender retries up to 3 times maximum
- If all retries fail, the communication is considered failed, and the master logs the error or takes corrective action
- This provides a clear limit and aligns with robust error-handling practices

**Re-send Request Strategy Clarification:**
The ZES protocol defines function code 0b000 as "re-send request" for both master and slave use. The implementation strategy is:

1. **Master-Initiated Retries**: When the master detects a CRC error or timeout, it re-sends the original command (with original function code) rather than sending a separate 0b000 frame. This is simpler and sufficient for most cases.

2. **Slave-Initiated Re-send Requests**: If a slave detects a CRC error in a received frame, it can send a re-send request frame (function code 0b000) to the master. However, the current API design focuses on master-initiated retries for simplicity.

3. **API Implementation**: The driver handles retries automatically by re-transmitting the original command. The 0b000 function code is primarily used internally for protocol compliance but is not directly exposed in the high-level API.

This approach provides robust error recovery while maintaining API simplicity.

#### 3.3.1 ID Byte Structure

The ID byte is structured as follows:

| Function Code (3 bits) | Device Address (5 bits) |
|:----------------------:|:-----------------------:|
| High 3 bits            | Low 5 bits              |

The 5-bit address field allows for a maximum of 31 nodes (30 slaves plus one master). Address 0x00 is reserved for broadcast messages. All AI-SLDAP boards are initialized with a default address of 0x00 during manufacturing.

Function codes:
- 0b111: Assign data (Master use)
- 0b110: Request data (Master use)
- 0b010: Response to Assign (Slave use)
- 0b001: Response to Request (Slave use)
- 0b000: Re-send request (Both use)

#### 3.3.2 Data Payload Structure by Category

The data payload consists of a JSON-style key-value pair as defined in the ZES protocol:

**Key-Value Pair Format:**
- **Key Field**: 4 bytes (higher 4 bytes of payload) - holds the name of the variable in ASCII code
- **Value Field**: 8 bytes (lower 8 bytes of payload) - holds the value of the variable in binary format

**Message Categories (as defined in ZES protocol):**

| Key Code | Category | Used For | Examples |
|----------|----------|----------|----------|
| **"Sxxx"** | System configurable data | Assign AI-SLDAP system parameters | S001 (slave address), S002 (baud rate) |
| **"Uxxx"** | User configurable data | Configure/control how AISL works | U001-U006 (various user settings) |
| **"Axxx"** | Application related data | Information/status reports | A001 (SEL event log), A002 (device status), A003 (firmware version), A004 (system statistics), A005 (current configuration) |
| **"Wxxx"** | Weights of AI model | Transmit weight parameters from PC to AISL | W001 (write), W002 (read) |

**Data Payload Structure by API Category:**

**Category 1: System Configuration (Broadcasting)**
- Key (4 bytes): Command identifier (e.g., "S001", "S002" in ASCII)
- Value (8 bytes): Configuration value to be assigned to the FPGA register

**Category 2: Master to Slave Request**
- Key (4 bytes): Data identifier for the requested information (e.g., "A001", "A002" in ASCII)
- Value (8 bytes): Reserved for future use (typically set to 0)

**Category 3: AI Model Weight and Bias Data**
- Key (4 bytes): Memory address in FRAM (for "W001", "W002" operations)
- Value (8 bytes): Data to be written (for write operations) or length of data to read (for read operations)

#### 3.3.3 User Interaction with Protocol

From the user's perspective, the protocol details are abstracted away. Users interact with the three API categories without needing to know about the ID byte structure or how the data is formatted in the payload. The driver automatically handles the construction of appropriate messages based on the API call, including setting the correct function code and device address in the ID byte.

### 3.4 Windows Driver Interface Structure

The RS485 driver is implemented as a Windows User-Mode Driver Framework (UMDF) driver, providing a native Windows driver interface instead of a traditional C++ class library. Applications interact with the driver through Windows I/O Control (IOCTL) calls using **DeviceIoControl()**.

**Data Exchange Mechanism:**
Data exchange between user-mode applications and the RS485 driver is achieved using **DeviceIoControl()** calls with specific IOCTL codes. This approach provides:

1. **Standard Windows Interface**: Uses the industry-standard Windows driver communication method
2. **Asynchronous I/O Support**: Enables non-blocking operations with overlapped I/O
3. **Buffer Management**: Efficient data transfer through system-managed buffers
4. **Error Handling**: Comprehensive error reporting through Windows error codes

**DeviceIoControl() Implementation Strategy:**
- **API Layer**: The high-level API functions (configureSystemSettings, requestData, etc.) internally use DeviceIoControl() calls
- **Abstraction**: Users interact with the simplified API interface without directly calling DeviceIoControl()
- **Internal Implementation**: Each API function translates to appropriate IOCTL codes and buffer management
- **FIFO Guarantee**: DeviceIoControl() calls are queued and processed in strict FIFO order to maintain data integrity

**Complete Driver Interface Architecture with Function Code Mapping:**
```cpp
// Windows Driver Interface - Application Side
class AI_SLDAP_RS485_DriverInterface {
public:
    // Constructor and basic operations
    AI_SLDAP_RS485_DriverInterface();
    ~AI_SLDAP_RS485_DriverInterface();

    // ===== ERROR HANDLE API (Function Code: 0b000) =====
    // FTDI-style management functions
    RS485Error openPort(const std::wstring& devicePath);
    RS485Error closePort();
    bool isPortOpen() const;
    RS485Error getPortInfo(PortInfo& info);

    // Device enumeration (similar to FTDI FT_ListDevices)
    static RS485Error enumerateDevices(std::vector<DeviceInfo>& deviceList);
    static RS485Error detectMultipleDevices(std::vector<uint8_t>& detectedAddresses);

    // Buffer management - CRITICAL for data integrity
    RS485Error getBufferStatus(BufferStatus& status);
    RS485Error checkUplinkBufferFlag(bool& isFull);
    RS485Error checkDownlinkBufferFlag(bool& isFull);
    RS485Error clearBuffer(BufferType bufferType = BufferType::BOTH);
    RS485Error setBufferOverflowPolicy(BufferOverflowPolicy policy);
    RS485Error getBufferCapacity(uint32_t& uplinkFrames, uint32_t& downlinkFrames);

    // Hardware status (similar to FTDI FT_GetStatus)
    RS485Error getHardwareStatus(HardwareStatus& status);
    RS485Error getPerformanceMetrics(PerformanceMetrics& metrics);
    RS485Error getBaudRate(uint32_t& currentBaudRate);
    RS485Error getLineStatus(LineStatus& status);

    // Error handling and retry management
    const char* getErrorString(RS485Error error) const;
    using ErrorCallbackFn = std::function<void(RS485Error error, const char* message)>;
    void registerErrorCallback(ErrorCallbackFn callback);
    void unregisterErrorCallback();

    // ===== MASTER BROADCASTING API (Function Code: 0b111 for S-series) =====
    // Automatic buffer flag checking before transmission
    RS485Error configureSystemSettings(uint32_t commandKey, uint64_t value);
    RS485Error verifySystemConfig(uint32_t commandKey, uint64_t expectedValue, bool& isMatching);

    // ===== MASTER ASSIGN DATA API (Function Code: 0b111 for U/W-series) =====
    // Automatic buffer flag checking before transmission
    RS485Error configureUserSettings(uint32_t commandKey, uint64_t value);
    RS485Error modelDataOperation(uint8_t slaveAddress, uint32_t address,
                                 std::vector<uint8_t>& data, bool isWrite, uint32_t length = 0);

    // ===== MASTER REQUEST API (Function Code: 0b110 for A-series) =====
    // Automatic buffer flag checking before transmission
    RS485Error requestData(uint8_t slaveAddress, uint32_t dataKey, const RequestOptions* options = nullptr);

    // ===== SLAVE RESPONSE API (Function Codes: 0b010 and 0b001) =====
    // Automatic buffer flag checking before storing received data
    RS485Error receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                   bool waitForData = false, uint32_t timeout = 100);

    // Response callback registration for asynchronous handling
    using ResponseCallbackFn = std::function<void(uint8_t slaveAddress, const std::vector<uint8_t>& data)>;
    void registerResponseCallback(uint8_t slaveAddress, ResponseCallbackFn callback);
    void unregisterResponseCallback(uint8_t slaveAddress);

    // Buffer threshold monitoring
    RS485Error setBufferThreshold(uint32_t thresholdPercent);
    using BufferThresholdCallbackFn = std::function<void(uint32_t currentUsage, uint32_t totalSize)>;
    void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

private:
    // Windows driver communication
    HANDLE m_driverHandle;
    std::wstring m_devicePath;

    // IOCTL helper methods with automatic buffer checking
    RS485Error sendIOCTL(DWORD ioctlCode, void* inputBuffer, DWORD inputSize,
                        void* outputBuffer, DWORD outputSize, DWORD* bytesReturned = nullptr);

    // Buffer flag checking - called before every transmission
    RS485Error checkBufferBeforeTransmission();
    RS485Error checkBufferBeforeStorage(uint8_t deviceAddress);

    // Thread safety
    std::mutex m_apiMutex;

    // Current slave address for U-series commands
    uint8_t m_currentSlaveAddress;

    // Buffer management state
    BufferOverflowPolicy m_bufferOverflowPolicy;
    uint32_t m_bufferThresholdPercent;
};

// Driver-specific structures for buffer management
struct BufferStatus {
    uint32_t uplinkUsed;        // Used payload frames in uplink buffer (0-5)
    uint32_t uplinkTotal;       // Total uplink buffer capacity (5 frames)
    uint32_t downlinkUsed;      // Used payload frames in downlink buffer (0-10)
    uint32_t downlinkTotal;     // Total downlink buffer capacity (10 frames)
    uint32_t payloadSize;       // Size per payload (12 bytes)
    bool isUplinkFull;          // Uplink buffer full flag
    bool isDownlinkFull;        // Downlink buffer full flag
    bool isOverflowDetected;    // Buffer overflow status
    uint32_t totalFrameSize;    // Complete frame size (16 bytes including headers)
};

enum class BufferType {
    UPLINK,     // PC to device buffer
    DOWNLINK,   // Device to PC buffer
    BOTH        // Both buffers
};

enum class BufferOverflowPolicy {
    DISCARD_OLDEST,  // Discard oldest frame when buffer is full (default)
    DISCARD_NEWEST,  // Discard new frame when buffer is full
    TRIGGER_ERROR    // Return error when buffer is full
};
```

**Windows Driver IOCTL Codes:**
```cpp
// IOCTL codes for driver communication
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_MODEL_DATA_OP       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_BUFFER_POLICY   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HW_STATUS       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x808, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_GET_PERFORMANCE     CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x809, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CHECK_BUFFER_FLAGS  CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x80A, METHOD_BUFFERED, FILE_READ_ACCESS)
```

### 3.5 DeviceIoControl() Implementation Details

**API Design Philosophy:**
The DeviceIoControl() mechanism is implemented **internally within the API functions** rather than being exposed directly to users. This design provides:

1. **User-Friendly Interface**: Applications use high-level API functions (configureSystemSettings, requestData, etc.)
2. **Internal Implementation**: Each API function internally calls DeviceIoControl() with appropriate IOCTL codes
3. **Abstraction Layer**: Users don't need to understand IOCTL codes or buffer management details
4. **Industry Standard**: Follows standard serial port communication interface patterns

**Complete Buffer Management Implementation Example:**
```cpp
// Example: Internal implementation with comprehensive buffer checking
RS485Error AI_SLDAP_RS485_DriverInterface::configureSystemSettings(uint32_t commandKey, uint64_t value) {
    std::lock_guard<std::mutex> lock(m_apiMutex);

    // Step 1: Mandatory buffer flag checking before transmission
    RS485Error bufferCheck = checkBufferBeforeTransmission();
    if (bufferCheck != RS485Error::SUCCESS) {
        return bufferCheck;
    }

    // Step 2: Validate function code correspondence (S-series uses 0b111)
    if (!isValidSystemCommand(commandKey)) {
        return RS485Error::INVALID_COMMAND_KEY;
    }

    // Step 3: Prepare IOCTL input buffer with 12-byte payload
    struct {
        uint32_t key;           // 4 bytes - command key
        uint64_t value;         // 8 bytes - command value
        uint8_t functionCode;   // Function code 0b111 for assign data
        uint8_t slaveAddress;   // 0x00 for broadcast
        uint8_t reserved[2];    // Padding for alignment
    } inputBuffer = {
        commandKey,
        value,
        0b111,  // Assign data function code
        0x00,   // Broadcast address
        {0, 0}  // Reserved
    };

    // Step 4: Call DeviceIoControl() with buffer management
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        m_driverHandle,                    // Device handle
        IOCTL_RS485_CONFIGURE_SYSTEM,      // IOCTL code
        &inputBuffer,                      // Input buffer
        sizeof(inputBuffer),               // Input buffer size
        nullptr,                           // Output buffer (not needed)
        0,                                 // Output buffer size
        &bytesReturned,                    // Bytes returned
        nullptr                            // Overlapped (for async operation)
    );

    if (!result) {
        DWORD lastError = GetLastError();
        return mapWindowsErrorToRS485Error(lastError);
    }

    // Step 5: Wait for acknowledgment (function code 0b010)
    std::vector<uint8_t> responseData;
    RS485Error ackResult = receiveSlaveResponse(0x00, responseData, true, 1000);

    return ackResult;
}

// Buffer checking implementation
RS485Error AI_SLDAP_RS485_DriverInterface::checkBufferBeforeTransmission() {
    BufferStatus status;
    RS485Error result = getBufferStatus(status);
    if (result != RS485Error::SUCCESS) {
        return result;
    }

    // Check uplink buffer flag
    if (status.isUplinkFull) {
        // Apply configured overflow policy
        switch (m_bufferOverflowPolicy) {
            case BufferOverflowPolicy::DISCARD_OLDEST:
                // Clear oldest frame to make space
                clearBuffer(BufferType::UPLINK);
                break;

            case BufferOverflowPolicy::DISCARD_NEWEST:
                // Reject new data
                return RS485Error::BUFFER_OVERFLOW;

            case BufferOverflowPolicy::TRIGGER_ERROR:
                // Return error for application handling
                return RS485Error::INSUFFICIENT_BUFFER;
        }
    }

    // Check if buffer usage exceeds threshold
    uint32_t usagePercent = (status.uplinkUsed * 100) / status.uplinkTotal;
    if (usagePercent >= m_bufferThresholdPercent) {
        // Trigger threshold callback if registered
        if (m_bufferThresholdCallback) {
            m_bufferThresholdCallback(status.uplinkUsed, status.uplinkTotal);
        }
    }

    return RS485Error::SUCCESS;
}

// Function code validation
bool AI_SLDAP_RS485_DriverInterface::isValidSystemCommand(uint32_t commandKey) {
    // S-series commands: S001, S002
    return (commandKey == 0x53303031 || commandKey == 0x53303032);
}
```

**Buffer Flag Management:**
```cpp
// Buffer flag checking mechanism
struct BufferFlags {
    bool uplinkFull;        // Uplink buffer full flag
    bool downlinkFull;      // Downlink buffer full flag
    uint32_t uplinkUsed;    // Current uplink usage (0-5)
    uint32_t downlinkUsed;  // Current downlink usage (0-10)
};

// Check buffer flags before operations
RS485Error checkBufferFlags(BufferFlags& flags) {
    return sendIOCTL(IOCTL_RS485_CHECK_BUFFER_FLAGS, nullptr, 0, &flags, sizeof(flags));
}
```

## 4. Detailed API Reference

### 4.0 Error Codes

```cpp
enum class RS485Error {
    // Success code
    SUCCESS = 0,            // Operation completed successfully

    // FTDI Driver related errors (100-199) - Inherited from FTDI VCP Driver
    // Transient errors - may succeed on retry
    CONNECTION_ERROR = 100, // Failed to open/connect to the serial port (similar to FT_IO_ERROR)
    DEVICE_BUSY = 102,      // Device is busy processing another command (similar to FT_DEVICE_NOT_OPENED)
    PORT_NOT_AVAILABLE = 103, // COM port not available or in use by another application
    DRIVER_NOT_LOADED = 104,  // FTDI VCP driver not properly loaded
    INSUFFICIENT_RESOURCES = 105, // System resources insufficient for operation

    // Permanent errors - require user intervention
    DEVICE_NOT_FOUND = 101, // Specified device not found (similar to FT_DEVICE_NOT_FOUND)
    INVALID_HANDLE = 106,   // Invalid device handle (similar to FT_INVALID_HANDLE)
    INVALID_BAUD_RATE = 107, // Unsupported baud rate specified
    INVALID_PARAMETER = 108, // Invalid parameter provided to function (similar to FT_INVALID_PARAMETER)

    // Buffer Management errors (150-199) - Critical for RS485 operation
    // Transient errors - may succeed on retry
    INSUFFICIENT_BUFFER = 150, // Buffer too small for operation
    BUFFER_OVERFLOW = 151,     // Buffer overflow detected
    BUFFER_UNDERFLOW = 152,    // Attempting to read from empty buffer

    // Permanent errors - require user intervention
    BUFFER_ALLOCATION_FAILED = 153, // Failed to allocate driver-managed buffers
    INVALID_BUFFER_SIZE = 154,      // Invalid buffer size specified

    // ZES Protocol related errors (200-299)
    // Transient errors - may succeed on retry
    PROTOCOL_ERROR = 200,   // Protocol violation detected
    CRC_ERROR = 201,        // CRC check failed (triggers automatic retry with 0b000)
    TIMEOUT_ERROR = 202,    // Command timed out waiting for response
    FRAME_SYNC_ERROR = 203, // Frame synchronization lost (header/trailer mismatch)
    RETRY_LIMIT_EXCEEDED = 204, // Maximum retry attempts exceeded

    // Permanent errors - require user intervention
    UNSUPPORTED_OPERATION = 205, // Operation not supported by device
    INVALID_COMMAND_KEY = 206,   // Invalid command key provided
    INVALID_SLAVE_ADDRESS = 207, // Invalid slave address (must be 1-31)
    BROADCAST_CONFLICT = 208,    // Multiple slaves detected during broadcast operation

    // Memory operation errors (300-399)
    // Transient errors - may succeed on retry
    MEMORY_ACCESS_ERROR = 300, // Error accessing FRAM memory (for AI model data)
    MEMORY_WRITE_FAILED = 301, // Failed to write to FRAM memory
    MEMORY_READ_FAILED = 302,  // Failed to read from FRAM memory

    // Permanent errors - require user intervention
    INVALID_MEMORY_ADDRESS = 303, // Invalid memory address specified
    MEMORY_RANGE_ERROR = 304,     // Memory address out of valid range
    MEMORY_PROTECTION_ERROR = 305, // Attempting to write to protected memory

    // Data handling errors (400-499)
    // Permanent errors - require user intervention
    DATA_FORMAT_ERROR = 400,    // Data format does not match expected format
    PAYLOAD_SIZE_ERROR = 401,   // Payload size exceeds 12-byte limit
    JSON_PARSE_ERROR = 402,     // Failed to parse JSON response data
    CHECKSUM_MISMATCH = 403,    // Data checksum verification failed

    // Function Code Processing errors (500-599)
    // Permanent errors - require user intervention
    INVALID_FUNCTION_CODE = 500, // Invalid function code in ID byte
    FUNCTION_CODE_MISMATCH = 501, // Function code doesn't match expected API category
    RESPONSE_TYPE_ERROR = 502,    // Unexpected response type received
    API_CATEGORY_ERROR = 503      // API call doesn't match function code requirements
};
```

**Enhanced Error Handling with FTDI Integration:**

The error handling system integrates FTDI VCP driver errors with ZES protocol errors, providing comprehensive error management:

1. **FTDI Driver Errors (100-199)**: Direct mapping from FTDI error codes to RS485 error codes
2. **Buffer Management Errors (150-199)**: Critical for reliable RS485 communication
3. **Protocol Errors (200-299)**: ZES protocol-specific error handling
4. **Function Code Errors (500-599)**: Ensures proper API category to function code correspondence

**Error Categorization and Recovery Strategies:**

This categorization of errors into transient (may succeed on retry) and permanent (require user intervention) helps applications implement more intelligent error handling and recovery strategies.

- **Transient errors** (e.g., `TIMEOUT_ERROR`, `CRC_ERROR`, `DEVICE_BUSY`) may succeed on retry. Applications should implement retry logic (see Section 4.0.1).
- **Permanent errors** (e.g., `INVALID_PARAMETER`, `DEVICE_NOT_FOUND`) require user intervention and should not be retried automatically.

#### 4.0.1 Intelligent Error Handling with Retry Logic

For transient errors, applications can implement retry logic to automatically recover from temporary issues:

```cpp
// Example of retry logic for transient errors
RS485Error requestWithRetry(AI_SLDAP_RS485_Driver& driver, uint8_t slaveAddress,
                           uint32_t dataKey, int maxRetries = 3, int retryDelayMs = 100) {
    RS485Error status;
    int retries = 0;

    do {
        status = driver.requestData(slaveAddress, dataKey);

        // Check if error is transient and we have retries left
        if ((status == RS485Error::TIMEOUT_ERROR ||
             status == RS485Error::CRC_ERROR ||
             status == RS485Error::DEVICE_BUSY) && retries < maxRetries) {

            std::this_thread::sleep_for(std::chrono::milliseconds(retryDelayMs));
            retries++;
            std::cout << "Retrying operation, attempt " << retries << " of " << maxRetries << std::endl;
        } else {
            // Either success or permanent error (or out of retries)
            break;
        }
    } while (true);

    return status;
}
```

#### 4.0.2 Error Callback Registration

The driver provides a mechanism to register error callbacks for asynchronous error notification:

```cpp
// Error callback function type
using ErrorCallbackFn = std::function<void(RS485Error error, const char* message)>;

// Register an error callback
void registerErrorCallback(ErrorCallbackFn callback);

// Unregister the error callback
void unregisterErrorCallback();
```

This allows applications to be notified of errors that occur during asynchronous operations without having to poll for error status.

```cpp
// Enhanced error callback example with suggested actions
driver.registerErrorCallback([](RS485Error error, const char* message) {
    if (error == RS485Error::TIMEOUT_ERROR) {
        std::cerr << "Transient: " << message << " - Retrying..." << std::endl;
        // Application can implement automatic retry here
    } else if (error == RS485Error::CRC_ERROR) {
        std::cerr << "Transient: " << message << " - Frame corrupted, retrying..." << std::endl;
        // CRC errors are automatically retried by the driver
    } else if (error == RS485Error::INVALID_PARAMETER) {
        std::cerr << "Permanent: " << message << " - Check parameters." << std::endl;
        // User intervention required - check command keys and values
    } else if (error == RS485Error::DEVICE_NOT_FOUND) {
        std::cerr << "Permanent: " << message << " - Check hardware connections." << std::endl;
        // User intervention required - verify device connection
    } else {
        // General categorization
        bool isTransient = (error == RS485Error::DEVICE_BUSY ||
                           error == RS485Error::PROTOCOL_ERROR ||
                           error == RS485Error::MEMORY_ACCESS_ERROR);

        if (isTransient) {
            std::cerr << "Transient error: " << message << " (may resolve with retry)" << std::endl;
        } else {
            std::cerr << "Permanent error: " << message << " (requires user intervention)" << std::endl;
        }
    }
});
```


### 4.1 Common Structures

```cpp
// Structure for device information
struct DeviceInfo {
    std::string port;       // COM port name
    std::string description;// Device description
    std::string serialNumber;// Serial number (if available)
};

// Hardware status structure
struct HardwareStatus {
    bool isConnected;           // Connection status
    uint32_t ftdiChipStatus;    // FTDI chip status flags
    uint32_t bufferOverflows;   // Number of buffer overflow events
    uint32_t crcErrors;         // Total CRC errors detected
    uint32_t timeoutErrors;     // Total timeout errors
    double signalStrength;      // RS485 signal strength (if available)
    uint32_t framesSent;        // Total frames transmitted
    uint32_t framesReceived;    // Total frames received successfully
};

// Performance metrics structure (enhanced from Section 6.1)
struct PerformanceMetrics {
    double avgLatencyMs;        // Average response latency in milliseconds
    uint32_t bytesPerSecond;    // Throughput in bytes per second
    uint32_t successfulFrames;  // Number of successful frame transmissions
    uint32_t failedFrames;      // Number of failed frame transmissions
    uint32_t retryCount;        // Total number of retries performed
    double frameSuccessRate;    // Success rate as percentage (0-100)
    double avgBusUtilization;   // Average bus utilization percentage
    uint32_t maxResponseTimeMs; // Maximum response time observed
};

// Note: The API design uses direct parameter passing for most operations, eliminating the need for complex structures
// This makes the API more concise and intuitive, with a unified format for each category
```

### 4.2 Windows Driver Implementation Details

#### 4.2.1 Filter Driver Architecture Components

**UMDF Filter Driver Structure:**
The RS485 driver is implemented as a UMDF Filter Driver using the following Windows Driver Framework components:

```cpp
// Filter Driver object callbacks
class RS485FilterDriver : public CUnknown, public IDriverEntry
{
public:
    // IDriverEntry implementation
    STDMETHOD(OnInitialize)(IWDFDriver* pDriver);
    STDMETHOD(OnDeviceAdd)(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit);
    STDMETHOD(OnDeinitialize)();

private:
    // Driver-managed buffers (focused on 12-byte payload data)
    RS485PayloadBuffer m_uplinkBuffer;    // 5 payloads × 12 bytes = 60 bytes
    RS485PayloadBuffer m_downlinkBuffer;  // 10 payloads × 12 bytes = 120 bytes

    // Buffer flags for overflow prevention
    volatile bool m_uplinkBufferFull;     // Uplink buffer full flag
    volatile bool m_downlinkBufferFull;   // Downlink buffer full flag

    // Communication with lower FTDI VCP driver
    IWDFIoTarget* m_pLowerDeviceTarget;  // Target for FTDI VCP driver
    IWDFFile* m_pSerialPortFile;         // Serial port file handle

    // Non-blocking I/O management
    IWDFWorkItem* m_pReceiveWorkItem;    // Asynchronous receive processing
    IWDFWorkItem* m_pTransmitWorkItem;   // Asynchronous transmit processing
    WDFQUEUE m_pendingRequestQueue;      // Queue for pending user requests

    // Frame processing pipeline
    FrameProcessor m_frameProcessor;     // State machine for frame parsing
    CRC8Calculator m_crcCalculator;     // Hardware-accelerated CRC calculation

    // Thread synchronization (never blocks user threads)
    WDFSPINLOCK m_bufferLock;           // Protects buffer access
    WDFDPC m_frameProcessingDpc;        // DPC for frame processing
};

// Payload buffer management (focused on 12-byte payload data)
class RS485PayloadBuffer
{
private:
    static const size_t PAYLOAD_SIZE = 12;  // Core protocol data size
    uint8_t* m_buffer;
    size_t m_capacity;      // Number of payload slots
    size_t m_head;          // Write position
    size_t m_tail;          // Read position
    size_t m_count;         // Current payload count
    CRITICAL_SECTION m_lock;

public:
    RS485PayloadBuffer(size_t payloadCount);
    ~RS485PayloadBuffer();

    // Core payload operations - FIFO guaranteed
    bool PushPayload(const uint8_t* payloadData);
    bool PopPayload(uint8_t* payloadData);

    // Buffer status and flag management
    size_t GetUsedPayloads() const { return m_count; }
    size_t GetTotalPayloads() const { return m_capacity; }
    bool IsFull() const { return m_count == m_capacity; }
    bool IsEmpty() const { return m_count == 0; }
    void Clear();

    // Buffer flag checking (critical for overflow prevention)
    bool CheckSpaceAvailable() const { return !IsFull(); }
    bool CheckDataAvailable() const { return !IsEmpty(); }

    // FIFO integrity verification
    bool VerifyFIFOIntegrity() const;
};

// Non-blocking frame processing implementation
class FrameProcessor {
private:
    FrameState m_currentState;
    uint8_t m_frameBuffer[16];
    size_t m_bytesReceived;
    uint32_t m_frameTimeout;
    LARGE_INTEGER m_lastByteTime;

public:
    // Called from DPC context - NEVER blocks, processes one byte at a time
    FrameProcessResult ProcessIncomingByte(uint8_t byte) {
        KeQuerySystemTime(&m_lastByteTime);

        switch (m_currentState) {
            case FrameState::WAITING_HEADER:
                if (byte == 0xAA) {
                    m_frameBuffer[0] = byte;
                    m_bytesReceived = 1;
                    m_currentState = FrameState::READING_ID;
                    return FrameProcessResult::CONTINUE;
                }
                return FrameProcessResult::CONTINUE;

            case FrameState::READING_ID:
                m_frameBuffer[m_bytesReceived++] = byte;
                m_currentState = FrameState::READING_PAYLOAD;
                return FrameProcessResult::CONTINUE;

            case FrameState::READING_PAYLOAD:
                m_frameBuffer[m_bytesReceived++] = byte;
                if (m_bytesReceived == 14) {  // Header + ID + 12 payload bytes
                    m_currentState = FrameState::READING_CRC;
                }
                return FrameProcessResult::CONTINUE;

            case FrameState::READING_CRC:
                m_frameBuffer[m_bytesReceived++] = byte;
                m_currentState = FrameState::READING_TRAILER;
                return FrameProcessResult::CONTINUE;

            case FrameState::READING_TRAILER:
                m_frameBuffer[m_bytesReceived++] = byte;
                if (byte == 0x0D && m_bytesReceived == 16) {
                    m_currentState = FrameState::FRAME_COMPLETE;
                    return FrameProcessResult::FRAME_READY;
                } else {
                    // Frame error - reset state machine
                    ResetStateMachine();
                    return FrameProcessResult::FRAME_ERROR;
                }

            default:
                ResetStateMachine();
                return FrameProcessResult::FRAME_ERROR;
        }
    }

    // Called from work item context - can perform complex operations
    void ProcessCompleteFrame(const RS485Frame& frame) {
        // Validate CRC8
        uint8_t calculatedCRC = CalculateCRC8(&frame.id_byte, 13);
        if (calculatedCRC != frame.crc8) {
            // Schedule re-send request
            ScheduleResendRequest(frame);
            return;
        }

        // Extract the 12-byte payload (CORE PROTOCOL DATA)
        uint8_t payload[12];
        memcpy(payload, frame.payload, 12);

        // Route frame to appropriate buffer based on function code
        uint8_t functionCode = (frame.id_byte >> 5) & 0x07;
        uint8_t deviceAddress = frame.id_byte & 0x1F;

        switch (functionCode) {
            case 0b001:  // Response to Request
            case 0b010:  // Response to Assign
                // Check downlink buffer flag before storing payload
                if (CheckDownlinkBufferFlag()) {
                    StorePayloadInDownlinkBuffer(payload, deviceAddress);
                    NotifyWaitingApplication(deviceAddress);
                } else {
                    // Buffer full - apply overflow policy
                    HandleDownlinkBufferOverflow(payload, deviceAddress);
                }
                break;

            case 0b000:  // Re-send request
                HandleResendRequest(frame);
                break;

            default:
                // Unexpected frame type
                LogFrameError(frame, "Unexpected function code");
                break;
        }
    }

private:
    void ResetStateMachine() {
        m_currentState = FrameState::WAITING_HEADER;
        m_bytesReceived = 0;
        RtlZeroMemory(m_frameBuffer, sizeof(m_frameBuffer));
    }

    // All these methods are non-blocking and use work items for complex operations
    void ScheduleResendRequest(const RS485Frame& frame);

    // Payload-focused buffer management with flag checking
    bool CheckUplinkBufferFlag() const;
    bool CheckDownlinkBufferFlag() const;
    void StorePayloadInDownlinkBuffer(const uint8_t* payload, uint8_t deviceAddress);
    void HandleDownlinkBufferOverflow(const uint8_t* payload, uint8_t deviceAddress);
    void HandleUplinkBufferOverflow(const uint8_t* payload);

    void NotifyWaitingApplication(uint8_t deviceAddress);
    void HandleResendRequest(const RS485Frame& frame);
    void LogFrameError(const RS485Frame& frame, const char* reason);

    // FIFO integrity maintenance
    void VerifyBufferFIFOIntegrity();
    void EnforceStrictFIFOOrdering();
};
```

**Driver Installation Files:**
- **RS485Driver.inf**: Windows Information File for driver installation
- **RS485Driver.dll**: User-mode driver binary
- **RS485DriverInterface.lib**: Application interface library
- **RS485DriverInterface.h**: Header file for application development

#### 4.2.2 Device Enumeration through Windows Device Manager

**Supported Operating Systems**
Windows (10, 11) - WDK-based driver requires Windows 10 or later

**Summary**
This function enumerates RS485 devices through the Windows Device Manager, providing native Windows device integration.

**Definition**
```cpp
static RS485Error enumerateDevices(std::vector<DeviceInfo>& deviceList)
```

**Parameters**
deviceList - Reference to a vector that will be filled with device information

**Return Value**
RS485Error::SUCCESS if successful, otherwise an error code

**Remarks**
The function uses Windows Device Manager APIs to enumerate devices. Unlike COM port enumeration, this provides direct access to the driver interface and includes additional device metadata.

**Enhanced DeviceInfo Structure:**
```cpp
struct DeviceInfo {
    std::string port;           // Device path for driver communication
    std::string description;    // Device description from Windows
    std::string serialNumber;   // Serial number (if available)
    std::string driverVersion;  // Driver version information
    bool isDriverLoaded;        // Whether the driver is currently loaded
    uint32_t vendorId;          // USB Vendor ID
    uint32_t productId;         // USB Product ID
};
```

**Example**
```cpp
std::vector<DeviceInfo> devices;
RS485Error result = AI_SLDAP_RS485_DriverInterface::enumerateDevices(devices);
if (result == RS485Error::SUCCESS) {
    for (const auto& device : devices) {
        std::cout << "Device Path: " << device.port << std::endl;
        std::cout << "Description: " << device.description << std::endl;
        std::cout << "Serial Number: " << device.serialNumber << std::endl;
        std::cout << "Driver Version: " << device.driverVersion << std::endl;
        std::cout << "Driver Loaded: " << (device.isDriverLoaded ? "Yes" : "No") << std::endl;
        std::cout << "VID:PID: " << std::hex << device.vendorId << ":" << device.productId << std::dec << std::endl;
        std::cout << "---" << std::endl;
    }
} else {
    std::cerr << "Failed to enumerate devices: "
              << AI_SLDAP_RS485_DriverInterface::getErrorString(result) << std::endl;
}
```

#### 4.2.3 Windows Driver Development and Deployment

**Development Environment Setup:**
1. **Visual Studio 2022**: With Desktop development with C++ workload
2. **Windows Driver Kit (WDK)**: Latest version for Windows 10/11
3. **Required Components**:
   - MSVC v143 - VS 2022 C++ x64/x86 Spectre-mitigated libs
   - C++ ATL for latest v143 build tools with Spectre Mitigations
   - Windows Driver Kit extension for Visual Studio

**Driver Project Structure:**
```
RS485Driver/
├── Driver/                     # UMDF driver implementation
│   ├── RS485Driver.cpp        # Main driver entry point
│   ├── RS485Device.cpp        # Device object implementation
│   ├── RS485Queue.cpp         # I/O queue management
│   ├── RS485Buffer.cpp        # Buffer management implementation
│   ├── RS485Driver.def        # Driver export definitions
│   └── RS485Driver.inf        # Driver installation information
├── Interface/                  # Application interface library
│   ├── RS485DriverInterface.cpp
│   ├── RS485DriverInterface.h
│   └── RS485DriverInterface.lib
├── Test/                      # Test applications
│   ├── RS485Test.cpp
│   └── RS485Test.exe
└── Package/                   # Driver package for distribution
    ├── RS485Driver.cat       # Catalog file (signed)
    ├── RS485Driver.inf       # Installation information
    └── RS485Driver.dll       # Driver binary
```

**Driver Installation Process:**
1. **Development Testing**: Use Visual Studio's driver deployment features
2. **Test Signing**: Enable test signing on development machines
3. **Production Signing**: Use code signing certificate for distribution
4. **Device Manager Integration**: Automatic installation through Windows Update or manual INF installation

**INF File Example (Filter Driver):**
```ini
[Version]
Signature="$WINDOWS NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
CatalogFile=RS485FilterDriver.cat
DriverVer=12/15/2024,1.0.0.0
PnpLockdown=1

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6001

[RS485Filter_Install]
CopyFiles=UMDriverCopy
; This is a filter driver, so we don't replace the function driver
; Instead, we add ourselves as an upper filter

[RS485Filter_Install.Services]
AddService = , 0x00000002   ; null service install

[RS485Filter_Install.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install
UmdfServiceOrder=RS485FilterDriver
; Configure as upper filter driver
UmdfKernelModeClientPolicy=AllowKernelModeClients
UmdfFileObjectPolicy=AllowNullAndUnknownFileObjects
UmdfFsContextUsePolicy=CanUseFsContext2

[RS485Filter_Install]
UmdfLibraryVersion=$UMDFVERSION$
ServiceBinary=%12%\UMDF\RS485FilterDriver.dll
; Filter driver specific settings
UmdfDispatcher=FileHandle
UmdfImpersonationLevel=Impersonation

[RS485Filter_Install.HW]
; Add this driver as an upper filter for FTDI devices
AddReg=RS485Filter_AddReg

[RS485Filter_AddReg]
HKR,,"UpperFilters",0x00010008,"RS485FilterDriver"

[UMDriverCopy]
RS485FilterDriver.dll

[DestinationDirs]
UMDriverCopy=12,UMDF

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="AI-SLDAP RS485 Filter Driver"
```

**Filter Driver Installation Notes:**
- The filter driver is installed as an **Upper Filter** above the existing FTDI VCP driver
- The FTDI VCP function driver remains as the primary function driver
- Our filter driver intercepts and processes I/O requests before passing them to the FTDI driver
- This approach maintains compatibility with existing FTDI driver infrastructure

#### 4.2.4 Buffer Flag Management and FIFO Guarantee Implementation

**Critical Buffer Flag Checking Mechanism:**

The RS485 driver implements a comprehensive buffer flag checking system to prevent data loss and ensure reliable communication:

```cpp
// Buffer flag checking implementation
class BufferFlagManager {
private:
    volatile bool m_uplinkBufferFull;
    volatile bool m_downlinkBufferFull;
    CRITICAL_SECTION m_flagLock;

public:
    // Pre-transmission buffer flag check
    bool CheckUplinkSpaceAvailable() {
        EnterCriticalSection(&m_flagLock);
        bool spaceAvailable = !m_uplinkBufferFull;
        LeaveCriticalSection(&m_flagLock);
        return spaceAvailable;
    }

    // Pre-storage buffer flag check
    bool CheckDownlinkSpaceAvailable() {
        EnterCriticalSection(&m_flagLock);
        bool spaceAvailable = !m_downlinkBufferFull;
        LeaveCriticalSection(&m_flagLock);
        return spaceAvailable;
    }

    // Update flags after buffer operations
    void UpdateBufferFlags(size_t uplinkUsed, size_t downlinkUsed) {
        EnterCriticalSection(&m_flagLock);
        m_uplinkBufferFull = (uplinkUsed >= 5);      // 5 payload slots
        m_downlinkBufferFull = (downlinkUsed >= 10); // 10 payload slots
        LeaveCriticalSection(&m_flagLock);
    }
};
```

**FIFO Guarantee Implementation:**

The driver ensures strict First-In-First-Out ordering for both PC User side and driver side:

```cpp
// FIFO integrity enforcement
class FIFOManager {
private:
    uint32_t m_sequenceNumber;
    std::queue<uint32_t> m_uplinkSequence;
    std::queue<uint32_t> m_downlinkSequence;

public:
    // Assign sequence number to outgoing payload
    uint32_t AssignSequenceNumber() {
        return ++m_sequenceNumber;
    }

    // Verify FIFO ordering on payload retrieval
    bool VerifyFIFOOrder(uint32_t expectedSequence, bool isUplink) {
        if (isUplink) {
            if (m_uplinkSequence.empty() || m_uplinkSequence.front() != expectedSequence) {
                return false; // FIFO violation detected
            }
            m_uplinkSequence.pop();
        } else {
            if (m_downlinkSequence.empty() || m_downlinkSequence.front() != expectedSequence) {
                return false; // FIFO violation detected
            }
            m_downlinkSequence.pop();
        }
        return true;
    }
};
```

**Payload-Centric Data Flow:**

The entire communication protocol revolves around the efficient handling of 12-byte payload data:

1. **Transmission Path**: PC User → Buffer Flag Check → 12-byte Payload Queue → Frame Assembly → RS485 Bus
2. **Reception Path**: RS485 Bus → Frame Parsing → 12-byte Payload Extraction → Buffer Flag Check → Payload Queue → PC User
3. **Core Principle**: All meaningful data exchange occurs through the 12-byte payload, making it the heart of the protocol

### 4.3 Error Handle API

#### 4.3.1 Overview

The Error Handle API provides detailed error information and handling capabilities, including COM port errors inherited from the FTDI driver. This API corresponds to the "Error Handle API" category in the ZES driver API classification.

```cpp
const char* getErrorString(RS485Error error) const
```

**Purpose:** Get a human-readable description of an error code

**Parameters:**
- `error`: The error code to get a description for

**Return Value:** A string describing the error

**Key Features:**
1. **Comprehensive Error Coverage**: Handles both driver-specific errors and COM port errors inherited from the FTDI driver
2. **Detailed Descriptions**: Provides specific information about what went wrong
3. **Internationalization Support**: Error messages can be localized for different languages

**Example:**
```cpp
RS485Error result = driver.open();
if (result != RS485Error::SUCCESS) {
    std::cerr << "Error: " << driver.getErrorString(result) << std::endl;
    // Handle the error appropriately
}
```

### 4.4 Windows Driver Connection Management

#### 4.4.1 Constructor

**Supported Operating Systems**
Windows (10, 11) - WDK-based driver requires Windows 10 or later

**Summary**
Creates a new instance of the RS485 driver interface for Windows driver communication.

**Definition**
```cpp
AI_SLDAP_RS485_DriverInterface()
```

**Parameters**
None - Windows driver uses device enumeration instead of port specification

**Return Value**
None (constructor)

**Remarks**
The constructor initializes the driver interface but does not open the connection. The Windows driver automatically manages buffer allocation (5 uplink + 10 downlink frames, 16 bytes each). Call `open()` with a device path to establish the connection.

**Example**
```cpp
// Create a driver interface instance
AI_SLDAP_RS485_DriverInterface driver;

// Enumerate available devices first
std::vector<DeviceInfo> devices;
RS485Error result = AI_SLDAP_RS485_DriverInterface::enumerateDevices(devices);
if (result == RS485Error::SUCCESS && !devices.empty()) {
    // Use the first available device
    std::wstring devicePath = std::wstring(devices[0].port.begin(), devices[0].port.end());
    driver.open(devicePath);
}
```

#### 4.4.2 open

**Supported Operating Systems**
Windows (10, 11)

**Summary**
Opens the RS485 connection through Windows driver interface.

**Definition**
```cpp
RS485Error open(const std::wstring& devicePath)
```

**Parameters**
devicePath - Windows device path (obtained from device enumeration)

**Return Value**
RS485Error::SUCCESS if successful, otherwise an error code

**Remarks**
This function establishes communication with the Windows driver. The device path should be obtained through device enumeration. The driver automatically initializes the fixed-size payload buffers (60 bytes uplink + 120 bytes downlink for 12-byte payload data).

**Example**
```cpp
AI_SLDAP_RS485_DriverInterface driver;

// Enumerate devices to get device path
std::vector<DeviceInfo> devices;
RS485Error result = AI_SLDAP_RS485_DriverInterface::enumerateDevices(devices);
if (result == RS485Error::SUCCESS && !devices.empty()) {
    std::wstring devicePath = std::wstring(devices[0].port.begin(), devices[0].port.end());

    RS485Error status = driver.open(devicePath);
    if (status != RS485Error::SUCCESS) {
        std::wcerr << L"Failed to open driver connection: "
                   << driver.getErrorString(status) << std::endl;
        return;
    }
    std::wcout << L"Driver connection opened successfully" << std::endl;
}
```

#### 4.4.3 close

**Supported Operating Systems**
Windows (10, 11)

**Summary**
Closes the RS485 driver connection.

**Definition**
```cpp
void close()
```

**Parameters**
None

**Return Value**
None

**Remarks**
Call this function when you are done using the device to release driver resources. The Windows driver automatically clears any remaining payload data in the buffers and resets buffer flags.

**Example**
```cpp
AI_SLDAP_RS485_DriverInterface driver;
std::wstring devicePath = L"\\\\?\\USB#VID_0403&PID_6001#...";

RS485Error status = driver.open(devicePath);
if (status == RS485Error::SUCCESS) {
    // Perform operations with the device

    // Close the connection when done
    driver.close();
    std::wcout << L"Driver connection closed" << std::endl;
}
```

#### 4.4.4 isOpen

**Supported Operating Systems**
Windows (10, 11)

**Summary**
Checks if the driver connection is open.

**Definition**
```cpp
bool isOpen() const
```

**Parameters**
None

**Return Value**
true if the driver connection is open, false otherwise

**Example**
```cpp
AI_SLDAP_RS485_DriverInterface driver;

// Check if connection is already open
if (!driver.isOpen()) {
    std::vector<DeviceInfo> devices;
    RS485Error result = AI_SLDAP_RS485_DriverInterface::enumerateDevices(devices);
    if (result == RS485Error::SUCCESS && !devices.empty()) {
        std::wstring devicePath = std::wstring(devices[0].port.begin(), devices[0].port.end());
        RS485Error status = driver.open(devicePath);
        if (status != RS485Error::SUCCESS) {
            std::wcerr << L"Failed to open driver connection" << std::endl;
            return;
        }
    }
}

// Perform operations with the device

// Check again before closing
if (driver.isOpen()) {
    driver.close();
}
```

#### 4.4.5 Buffer Status Monitoring

**Summary**
Monitor the driver-managed buffer status for both uplink and downlink communications.

**Definition**
```cpp
RS485Error getBufferStatus(BufferStatus& status)
```

**Parameters**
status - Reference to BufferStatus structure to receive current buffer information

**Return Value**
RS485Error::SUCCESS if successful, otherwise an error code

**Example**
```cpp
AI_SLDAP_RS485_DriverInterface driver;
// ... open connection ...

BufferStatus bufferStatus;
RS485Error result = driver.getBufferStatus(bufferStatus);
if (result == RS485Error::SUCCESS) {
    std::cout << "Uplink buffer: " << bufferStatus.uplinkUsed
              << "/" << bufferStatus.uplinkTotal << " payload slots used" << std::endl;
    std::cout << "Downlink buffer: " << bufferStatus.downlinkUsed
              << "/" << bufferStatus.downlinkTotal << " payload slots used" << std::endl;
    std::cout << "Payload size: " << bufferStatus.payloadSize << " bytes" << std::endl;
    std::cout << "Total frame size: " << bufferStatus.totalFrameSize << " bytes" << std::endl;

    // Check buffer flags
    if (bufferStatus.isUplinkFull) {
        std::cout << "Warning: Uplink buffer is full!" << std::endl;
    }
    if (bufferStatus.isDownlinkFull) {
        std::cout << "Warning: Downlink buffer is full!" << std::endl;
    }
    if (bufferStatus.isOverflowDetected) {
        std::cout << "Warning: Buffer overflow detected!" << std::endl;
    }
}
```

#### 4.4.6 Complete DeviceIoControl() Implementation Example

**Comprehensive API Implementation with Buffer Flag Checking:**

The following example demonstrates how the high-level API functions internally use DeviceIoControl() with proper buffer flag checking and FIFO guarantee:

```cpp
// Complete implementation example showing DeviceIoControl() usage
class AI_SLDAP_RS485_DriverInterface {
private:
    HANDLE m_driverHandle;
    std::mutex m_apiMutex;
    uint8_t m_currentSlaveAddress;

    // Internal DeviceIoControl() wrapper with buffer flag checking
    RS485Error sendPayloadWithBufferCheck(DWORD ioctlCode, const uint8_t* payload,
                                         size_t payloadSize, bool checkUplink = true) {
        std::lock_guard<std::mutex> lock(m_apiMutex);

        // Step 1: Check buffer flags before transmission
        if (checkUplink) {
            BufferFlags flags;
            RS485Error flagResult = checkBufferFlags(flags);
            if (flagResult != RS485Error::SUCCESS) {
                return flagResult;
            }

            if (flags.uplinkFull) {
                return RS485Error::INSUFFICIENT_BUFFER;
            }
        }

        // Step 2: Prepare IOCTL input buffer with 12-byte payload
        struct IOCTLInputBuffer {
            uint8_t payload[12];    // Core protocol data
            uint8_t slaveAddress;   // Target slave address
            uint8_t reserved[3];    // Padding for alignment
        } inputBuffer;

        // Clear and copy payload data
        memset(&inputBuffer, 0, sizeof(inputBuffer));
        memcpy(inputBuffer.payload, payload, min(payloadSize, 12));
        inputBuffer.slaveAddress = m_currentSlaveAddress;

        // Step 3: Call DeviceIoControl() with proper error handling
        DWORD bytesReturned;
        BOOL result = DeviceIoControl(
            m_driverHandle,                    // Device handle
            ioctlCode,                         // IOCTL code
            &inputBuffer,                      // Input buffer (12-byte payload + metadata)
            sizeof(inputBuffer),               // Input buffer size
            nullptr,                           // Output buffer (not needed for this operation)
            0,                                 // Output buffer size
            &bytesReturned,                    // Bytes returned
            nullptr                            // Overlapped (synchronous operation)
        );

        if (!result) {
            DWORD error = GetLastError();
            switch (error) {
                case ERROR_DEVICE_NOT_READY:
                    return RS485Error::DEVICE_BUSY;
                case ERROR_TIMEOUT:
                    return RS485Error::TIMEOUT_ERROR;
                case ERROR_INVALID_PARAMETER:
                    return RS485Error::INVALID_PARAMETER;
                default:
                    return RS485Error::PROTOCOL_ERROR;
            }
        }

        return RS485Error::SUCCESS;
    }

public:
    // High-level API implementation using DeviceIoControl()
    RS485Error configureUserSettings(uint32_t commandKey, uint64_t value) override {
        // Prepare 12-byte payload (Key + Value)
        uint8_t payload[12];
        memcpy(payload, &commandKey, 4);      // Key (4 bytes)
        memcpy(payload + 4, &value, 8);       // Value (8 bytes)

        // Send via DeviceIoControl() with buffer flag checking
        return sendPayloadWithBufferCheck(IOCTL_RS485_CONFIGURE_USER, payload, 12, true);
    }

    RS485Error requestData(uint8_t slaveAddress, uint32_t dataKey,
                          const RequestOptions* options = nullptr) override {
        // Temporarily set slave address for this request
        uint8_t previousAddress = m_currentSlaveAddress;
        m_currentSlaveAddress = slaveAddress;

        // Prepare 12-byte payload (Key + Reserved)
        uint8_t payload[12];
        memcpy(payload, &dataKey, 4);         // Key (4 bytes)
        memset(payload + 4, 0, 8);            // Reserved (8 bytes)

        // Send via DeviceIoControl() with buffer flag checking
        RS485Error result = sendPayloadWithBufferCheck(IOCTL_RS485_REQUEST_DATA, payload, 12, true);

        // Restore previous slave address
        m_currentSlaveAddress = previousAddress;
        return result;
    }

    RS485Error receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData,
                                   bool waitForData = false, uint32_t timeout = 100) override {
        std::lock_guard<std::mutex> lock(m_apiMutex);

        // Check downlink buffer flag for available data
        BufferFlags flags;
        RS485Error flagResult = checkBufferFlags(flags);
        if (flagResult != RS485Error::SUCCESS) {
            return flagResult;
        }

        if (!waitForData && flags.downlinkUsed == 0) {
            return RS485Error::INSUFFICIENT_BUFFER; // No data available
        }

        // Prepare IOCTL input/output buffers
        struct IOCTLReceiveInput {
            uint8_t slaveAddress;
            uint8_t waitForData;
            uint32_t timeout;
            uint8_t reserved[2];
        } inputBuffer = { slaveAddress, waitForData ? 1 : 0, timeout, {0, 0} };

        struct IOCTLReceiveOutput {
            uint8_t payload[12];    // 12-byte payload data
            uint32_t dataLength;    // Actual data length
            uint8_t reserved[4];    // Padding
        } outputBuffer;

        // Call DeviceIoControl() to retrieve payload data
        DWORD bytesReturned;
        BOOL result = DeviceIoControl(
            m_driverHandle,
            IOCTL_RS485_RECEIVE_RESPONSE,
            &inputBuffer,
            sizeof(inputBuffer),
            &outputBuffer,
            sizeof(outputBuffer),
            &bytesReturned,
            nullptr
        );

        if (!result) {
            return RS485Error::TIMEOUT_ERROR;
        }

        // Extract payload data and return to user
        responseData.clear();
        responseData.resize(outputBuffer.dataLength);
        memcpy(responseData.data(), outputBuffer.payload, outputBuffer.dataLength);

        return RS485Error::SUCCESS;
    }

private:
    // Buffer flag checking via DeviceIoControl()
    RS485Error checkBufferFlags(BufferFlags& flags) {
        DWORD bytesReturned;
        BOOL result = DeviceIoControl(
            m_driverHandle,
            IOCTL_RS485_CHECK_BUFFER_FLAGS,
            nullptr,
            0,
            &flags,
            sizeof(flags),
            &bytesReturned,
            nullptr
        );

        return result ? RS485Error::SUCCESS : RS485Error::PROTOCOL_ERROR;
    }
};
```

**Key Implementation Points:**

1. **DeviceIoControl() is Internal**: Users never directly call DeviceIoControl() - it's encapsulated within the API functions
2. **Buffer Flag Checking**: Every operation checks buffer flags before proceeding to prevent overflow
3. **12-byte Payload Focus**: All data exchange centers around the 12-byte payload which contains the core protocol information
4. **FIFO Guarantee**: The driver maintains strict FIFO ordering through proper buffer management
5. **Error Handling**: Comprehensive error mapping from Windows error codes to RS485Error enum
6. **Thread Safety**: Mutex protection ensures thread-safe access to the driver interface

### 4.5 Master Broadcasting API (System Configuration Commands)

#### 4.4.1 Overview and Implementation Strategy

The Master Broadcasting API enables PC applications to send broadcast frames to slave devices for system-level configuration and receive explicit success/failure responses. This API is specifically designed for S-series commands (S001, S002) and is implemented as a dedicated driver-level function.

```cpp
RS485Error configureSystemSettings(uint32_t commandKey, uint64_t value)
```

**Purpose:** Configure system parameters via broadcasting (S-series commands only)

**Parameters:**
- `commandKey`: 4-byte command key (ASCII values of S-series commands)
  - Valid values: 0x53303031 (S001), 0x53303032 (S002)
- `value`: 8-byte value to be set
  - For S001: 1-31 (slave address range)
  - For S002: 9600, 19200, 38400, 57600, 115200 (supported baud rates)

**Return Value:**
- `RS485Error::SUCCESS`: Configuration applied successfully
- `RS485Error::TIMEOUT_ERROR`: No acknowledgment received from slave
- `RS485Error::INVALID_PARAMETER`: Invalid commandKey or value range
- `RS485Error::DEVICE_BUSY`: Device is processing another command
- `RS485Error::CRC_ERROR`: Frame corruption detected

**Example:**
```cpp
// Set slave address to 5
RS485Error result = driver.configureSystemSettings(0x53303031, 5);
if (result != RS485Error::SUCCESS) {
    std::cerr << "Failed to set slave address: " << driver.getErrorString(result) << std::endl;
}
```

**Supported Commands:**

| Command | Description | Value Range | ASCII Key |
|---------|-------------|-------------|-----------|
| **S001** | Set RS485 slave address | 1-31 | 0x53303031 |
| **S002** | Set baud rate | 9600, 19200, 38400, 57600, 115200 | 0x53303032 |

#### 4.4.2 Broadcast Frame Implementation Details

**Implementation Decision:**
The broadcasting functionality is implemented as a dedicated API at the data link layer rather than in the application layer for several reasons:
1. It requires special handling of the acknowledgment mechanism
2. It has specific hardware requirements that must be enforced
3. It needs consistent behavior across all applications using the driver

**Broadcast vs. Normal Data Transmission:**
Broadcast frames are handled differently from normal data transmission in several ways:
1. They are sent to address 0x00 instead of a specific slave address
2. They require a special acknowledgment mechanism
3. They have specific hardware setup requirements

**Hardware Requirements:**
- **Single Slave Configuration**: When using the Broadcasting API, only one slave device (our AI-SLDAP device) must be connected to the RS485 bus
- **Isolation Requirement**: All other nodes must be physically disconnected from the bus during broadcast operations
- **Reason**: The broadcast acknowledgment mechanism cannot handle multiple slaves responding simultaneously, which would cause bus collisions

**Important Warning:** The Master Broadcasting API requires only one slave device connected to the RS485 bus to avoid collisions. Connecting multiple slaves during S001 or S002 commands will result in unpredictable behavior and communication failures.

**Broadcast Frame Usage (as defined in ZES protocol):**
- Currently, ZES protocol only uses broadcast frames to assign slave address (S001)
- Users can define application-related broadcast messages, such as sleep and wake up commands, by sending "Uxxx" messages to AISL
- All broadcast frames are sent to address 0x00 (reserved broadcast address)
- Only the master can send broadcast messages; slaves cannot initiate broadcast communication

**Enhanced Runtime Safety Checks:**
The driver implements enhanced runtime safety checks to prevent issues with broadcasting:

```cpp
RS485Error configureSystemSettings(uint32_t commandKey, uint64_t value) {
    // Enhanced multiple device detection
    if (detectMultipleDevices()) {
        std::cerr << "Error: Multiple slaves detected on the bus. "
                  << "Broadcasting requires a single slave device." << std::endl;
        return RS485Error::INVALID_PARAMETER; // Enforce single-slave requirement
    }

    // Validate command key and value ranges
    if (commandKey == 0x53303031 && (value < 1 || value > 31)) {
        return RS485Error::INVALID_PARAMETER; // S001 address out of range
    }
    if (commandKey == 0x53303032) {
        std::vector<uint64_t> validBaudRates = {9600, 19200, 38400, 57600, 115200};
        if (std::find(validBaudRates.begin(), validBaudRates.end(), value) == validBaudRates.end()) {
            return RS485Error::INVALID_PARAMETER; // S002 invalid baud rate
        }
    }

    // Store current slave address before attempting S001
    uint8_t previousSlaveAddress = currentSlaveAddress;

    // Send broadcast command and wait for acknowledgment
    RS485Error result = sendBroadcastCommand(commandKey, value);

    if (result == RS485Error::SUCCESS) {
        // Only update currentSlaveAddress on successful S001 acknowledgment
        if (commandKey == 0x53303031) {
            currentSlaveAddress = static_cast<uint8_t>(value);
        }
        // For S002 (baud rate change), automatically reconfigure PC COM port
        else if (commandKey == 0x53303032) {
            result = reconfigureComPortBaudRate(value);
            if (result != RS485Error::SUCCESS) {
                std::cerr << "Warning: Slave baud rate changed but PC COM port reconfiguration failed" << std::endl;
            }
        }
    } else {
        // On failure, currentSlaveAddress remains unchanged
        currentSlaveAddress = previousSlaveAddress;
    }

    return result;
}
```

**Multiple Device Detection Mechanism:**
The `detectMultipleDevices()` function works as follows:
- **Passive Check**: Based on device enumeration count and previous communication history
- **Best-Effort Warning**: This is a best-effort detection mechanism, not foolproof
- **Implementation**: Checks if multiple COM ports are detected or if previous communications indicated multiple responding devices
- **Limitation**: Cannot guarantee detection of all multi-device scenarios due to RS485 bus nature

This enhanced safety check helps prevent common issues including bus collisions, invalid parameter values, and state inconsistencies.

#### 4.4.3 Key Features

1. **Targeted Communication**: Although broadcast in nature (sent to address 0x00), this API is designed to interact with a specific slave device.
2. **Mandatory Acknowledgment**: Slave devices must reply with a confirmation message upon receiving a correctly formatted broadcast frame with valid CRC, replacing the less reliable timeout-based detection mechanism.
3. **Hardware Limitation Enforcement**: The driver includes checks to help ensure proper hardware configuration.

**Implementation Notes:**
- The driver automatically handles the construction of broadcast frames
- The driver waits for an acknowledgment response from the slave device
- If no response is received within the timeout period, the operation is considered failed
- The driver handles retries automatically if configured to do so

**Example:**
```cpp
// Configure slave address (S001) to 5
status = driver.configureSystemSettings(0x53303031, 5);
if (status != RS485Error::SUCCESS) {
    std::cerr << "Failed to set slave address: " << driver.getErrorString(status) << std::endl;
    return status;
}

// Configure baud rate (S002) to 115200
status = driver.configureSystemSettings(0x53303032, 115200);
if (status != RS485Error::SUCCESS) {
    std::cerr << "Failed to set baud rate: " << driver.getErrorString(status) << std::endl;
    return status;
}

// Important: After changing baud rate, both master and slave switch to the new rate
// The driver automatically handles the master-side baud rate change
// All subsequent communication will use the new baud rate (115200)
```

### 4.5 Master Assign Data API

The Master Assign Data API is used to assign data to slave devices, including both user configuration parameters (U-series commands) and AI model weights and bias data (W-series commands).

#### 4.5.1 User Configuration Commands (U-series)

```cpp
RS485Error configureUserSettings(uint32_t commandKey, uint64_t value)
```

**Purpose:** Configure user parameters on slave devices

**Parameters:**
- `commandKey`: 4-byte command key (ASCII values of U-series commands)
- `value`: 8-byte value to be set

**Return Value:** `RS485Error::SUCCESS` if successful, otherwise an error code

**Addressing Mechanism:**
- U-series commands sent via `configureUserSettings` use the slave address previously set by S001 command
- If no S001 command has been executed, the default address (0x00) is used
- This implicit addressing mechanism means you should set the slave address using S001 before sending U-series commands

**Important Note on Default Address (0x00):**
Sending U-series commands to address 0x00 means they are broadcast to all slaves. This might be unintended if the user forgets to set a specific slave address first. Consider the following:
- **Caution**: Broadcasting user configuration (U-series) might affect multiple devices unintentionally
- **Best Practice**: Always execute S001 to set a specific slave address before using U-series commands
- **Safety Check**: The driver can optionally warn when `configureUserSettings` is called with `currentSlaveAddress == 0x00`

```cpp
RS485Error configureUserSettings(uint32_t commandKey, uint64_t value) {
    // Optional safety check for broadcast address
    if (currentSlaveAddress == 0x00) {
        std::cerr << "Warning: Sending U-series command to broadcast address (0x00). "
                  << "Consider setting specific slave address with S001 first." << std::endl;
        // Optionally return error: return RS485Error::INVALID_PARAMETER;
    }

    // Proceed with command...
}
```

**Supported Commands:**

| Command | Description | Value Range | ASCII Key |
|---------|-------------|-------------|-----------|
| **U001** | Set SEL detection threshold | 40-500 milliampere | 0x55303031 |
| **U002** | Set SEL maximum amplitude threshold | 1000-2000 milliampere | 0x55303032 |
| **U003** | Set number of SEL detections before power cycle | 1-5 | 0x55303033 |
| **U004** | Set power cycle duration | 200, 400, 600, 800, or 1000 milliseconds | 0x55303034 |
| **U005** | Enable/disable GPIO input functions | 0/1 for channel, 0/1 for enable/disable | 0x55303035 |
| **U006** | Enable/disable GPIO output functions | 0/1 for channel, 0/1 for enable/disable | 0x55303036 |

**Example with Proper Addressing:**
```cpp
// First set the slave address using S001 (Master Broadcasting API)
status = driver.configureSystemSettings(0x53303031, 5);  // Set slave address to 5

// Now configure U-series parameters for slave address 5
// Configure SEL detection threshold (U001) to 250mA
status = driver.configureUserSettings(0x55303031, 250);

// Configure max amplitude threshold (U002) to 1500mA
status = driver.configureUserSettings(0x55303032, 1500);

// Configure SEL detection count (U003) to 3
status = driver.configureUserSettings(0x55303033, 3);

// Configure power cycle duration (U004) to 600ms
status = driver.configureUserSettings(0x55303034, 600);

// Configure GPIO input function (U005) - Channel 0, Enable=1
uint64_t gpio_input_value = (0 & 0xFF) | ((1 & 0xFF) << 8);  // Channel 0, Enable
status = driver.configureUserSettings(0x55303035, gpio_input_value);

// Configure GPIO output function (U006) - Channel 1, Enable=1
uint64_t gpio_output_value = (1 & 0xFF) | ((1 & 0xFF) << 8);  // Channel 1, Enable
status = driver.configureUserSettings(0x55303036, gpio_output_value);

// To configure a different slave, set a new slave address
status = driver.configureSystemSettings(0x53303031, 6);  // Set slave address to 6

// Now configure U-series parameters for slave address 6
status = driver.configureUserSettings(0x55303031, 300);  // Different threshold for slave 6
```

#### 4.5.2 Configuration Verification

To verify that a configuration has been successfully applied, you can use the following method:

```cpp
RS485Error verifySystemConfig(uint32_t commandKey, uint64_t expectedValue, bool& isMatching)
```

**Purpose:** Verify that a configuration value matches the expected value

**Parameters:**
- `commandKey`: 4-byte command key (ASCII values of U-series or S-series commands)
- `expectedValue`: 8-byte value that should be set
- `isMatching`: Reference to a boolean that will be set to true if the value matches, false otherwise

**Return Value:** `RS485Error::SUCCESS` if the verification was performed successfully, otherwise an error code

**Example:**
```cpp
// Verify SEL detection threshold
bool isMatching;
status = driver.verifySystemConfig(0x55303031, 250, isMatching);
if (status == RS485Error::SUCCESS) {
    if (isMatching) {
        std::cout << "SEL detection threshold is correctly set to 250mA" << std::endl;
    } else {
        std::cout << "SEL detection threshold does not match expected value" << std::endl;
    }
}
```

#### 4.5.3 AI Model Weight and Bias Data Commands (W-series)

```cpp
RS485Error modelDataOperation(uint8_t slaveAddress, uint32_t address,
                             std::vector<uint8_t>& data, bool isWrite, uint32_t length = 0)
```

**Purpose:** Manage AI model weights and bias data stored in FRAM memory

**Parameters:**
- `slaveAddress`: Address of the slave device (1-31)
- `address`: Memory address in FRAM
- `data`: Vector for writing or reading data
- `isWrite`: true for write operation, false for read operation
- `length`: Number of bytes to read (ignored for write operations)

**Return Value:** `RS485Error::SUCCESS` if successful, otherwise an error code

**Supported Commands:**

| Operation | Description | Parameters | ASCII Key |
|-----------|-------------|------------|-----------|
| **W001** | Write model data to FRAM | Memory address, data bytes | 0x57303031 |
| **W002** | Read model data from FRAM | Memory address, length | 0x57303032 |

**Key Features:**
1. **Unified Interface**: A single function handles both read and write operations
2. **Flexible Data Size**: Supports variable-length data transfers
3. **Direct Memory Access**: Operates directly on the FRAM memory of the slave device

**Example:**
```cpp
// Write model data
std::vector<uint8_t> writeData = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
status = driver.modelDataOperation(5, 0x1000, writeData, true);

// Read model data
std::vector<uint8_t> readData;
status = driver.modelDataOperation(5, 0x1000, readData, false, 8);
```

### 4.6 Master Request API

#### 4.6.1 Overview

The Master Request API enables PC applications to send requests to slave devices and receive responses without blocking the application thread for extended periods, which is crucial for maintaining responsive user interfaces. This API corresponds to the "Master Request API" category in the ZES driver API classification and follows industry-standard serial port communication patterns.

```cpp
RS485Error requestData(uint8_t slaveAddress, uint32_t dataKey, const RequestOptions* options = nullptr)
```

**Purpose:** Request information from slave devices

**Parameters:**
- `slaveAddress`: Address of the slave device (1-31)
- `dataKey`: 4-byte identifier for the requested data (A-series commands)
- `options`: Optional pointer to a structure containing request-specific options (filtering, pagination, etc.)

**Return Value:** `RS485Error::SUCCESS` if successful, otherwise an error code

**Key Features:**
1. **Non-Blocking Design**: After sending a request, the function returns immediately after receiving a basic acknowledgment from the slave, allowing the PC application thread to continue execution.
2. **Asynchronous Operation**: The underlying driver uses OS threads to handle the actual transmission, preventing the user thread from being blocked by slow serial transmission.
3. **Data Retrieval Separation**: The actual response data must be retrieved using the `receiveSlaveResponse` function, providing a clean separation between request and response handling.
4. **Parameterized Requests**: Support for optional parameters to filter or limit the data returned (especially useful for A001 and A004 commands).
5. **Industry-Standard Pattern**: Follows the common request-response pattern used in modern serial communication protocols.

**Request Options Structure:**
```cpp
struct RequestOptions {
    // Time range filtering (for event logs)
    uint64_t startTime = 0;        // Start time in Unix timestamp format (0 = no limit)
    uint64_t endTime = 0;          // End time in Unix timestamp format (0 = no limit)

    // Pagination
    uint32_t maxRecords = 0;       // Maximum number of records to return (0 = no limit)
    uint32_t offset = 0;           // Starting offset for pagination

    // Filtering
    uint16_t eventTypeFilter = 0;  // Bit flags for event types to include (0 = all)

    // Format options
    bool compactFormat = false;    // Use compact JSON format if true
};
```

**Slave-Side Interpretation of RequestOptions:**
While this is a PC driver API, it's helpful to understand how slaves might interpret these fields for A001/A004 JSON responses:

- **Time Filtering**: Slave filters events based on `startTime` and `endTime` timestamps before formatting JSON response
- **Pagination**: Slave returns `maxRecords` entries starting from `offset` position in its event log
- **Event Type Filtering**: Slave includes only event types matching the `eventTypeFilter` bit flags:
  - Bit 0: SEL detection events
  - Bit 1: Power cycle events
  - Bit 2: GPIO events
  - Bit 3: System status changes
- **Compact Format**: When `true`, slave returns minimal JSON without formatting/whitespace to reduce data size

This ensures consistent behavior between PC driver expectations and slave firmware implementation.

**Implementation Notes:**
- The function sends a request to the specified slave device
- The function waits for an initial acknowledgment from the slave device
- Once the acknowledgment is received, the function returns control to the application
- The actual data is retrieved asynchronously and stored in the driver buffer
- The application must later retrieve the data using the Slave Response API
- For complex data (A001, A004), the response is formatted as JSON for flexibility and extensibility

**Example:**
```cpp
// Request SEL event log from slave device with address 5
status = driver.requestData(5, 0x41303031);  // "A001" in ASCII

// Wait for and retrieve the response data
std::vector<uint8_t> responseData;
status = driver.receiveSlaveResponse(5, responseData, true, 200);  // Wait up to 200ms

// Process the JSON response data
if (status == RS485Error::SUCCESS && !responseData.empty()) {
    std::string jsonStr(responseData.begin(), responseData.end());
    std::cout << "SEL event log: " << jsonStr << std::endl;

    // Example of parsing the JSON (using a JSON library)
    // auto json = JSON::parse(jsonStr);
    // for (const auto& event : json["events"]) {
    //     std::cout << "Event ID: " << event["id"] << ", Type: " << event["type"]
    //               << ", Time: " << event["timestamp"] << std::endl;
    // }
}

// Request device status with filtering options
RequestOptions options;
options.startTime = 1620000000;  // Only events after this time
options.maxRecords = 10;         // Limit to 10 records
status = driver.requestData(5, 0x41303031, &options);  // A001 with filtering
status = driver.receiveSlaveResponse(5, responseData, true, 200);

// Request device status (simple binary response)
status = driver.requestData(5, 0x41303032);  // "A002" in ASCII
status = driver.receiveSlaveResponse(5, responseData, true, 200);
if (status == RS485Error::SUCCESS && responseData.size() >= 2) {
    uint16_t statusFlags = (responseData[0] << 8) | responseData[1];
    bool isSELDetected = (statusFlags & 0x0001) != 0;
    bool isPowerCycleActive = (statusFlags & 0x0002) != 0;
    std::cout << "SEL detected: " << isSELDetected << std::endl;
    std::cout << "Power cycle active: " << isPowerCycleActive << std::endl;
}

// Request firmware version
status = driver.requestData(5, 0x41303033);  // "A003" in ASCII
status = driver.receiveSlaveResponse(5, responseData, true, 200);
if (status == RS485Error::SUCCESS && !responseData.empty()) {
    std::string version(responseData.begin(), responseData.end());
    std::cout << "Firmware version: " << version << std::endl;
}

// Request system statistics
status = driver.requestData(5, 0x41303034);  // "A004" in ASCII
status = driver.receiveSlaveResponse(5, responseData, true, 200);
// Process JSON statistics data similar to A001

// Request current configuration
status = driver.requestData(5, 0x41303035);  // "A005" in ASCII
status = driver.receiveSlaveResponse(5, responseData, true, 200);
if (status == RS485Error::SUCCESS && !responseData.empty()) {
    std::string configJson(responseData.begin(), responseData.end());
    std::cout << "Current configuration: " << configJson << std::endl;

    // Example of parsing the configuration JSON (using a JSON library)
    // auto config = JSON::parse(configJson);
    // uint32_t threshold = config["configuration"]["user_settings"]["sel_detection_threshold"];
    // uint32_t baudRate = config["configuration"]["system_settings"]["baud_rate"];
    // std::cout << "SEL threshold: " << threshold << "mA, Baud rate: " << baudRate << std::endl;
}
```

#### 4.6.2 Optional Callback Mechanism

The driver also provides an optional callback mechanism for notification when response data is available:

```cpp
// Response callback function type
using ResponseCallbackFn = std::function<void(uint8_t slaveAddress, const std::vector<uint8_t>& data)>;

// Register a response callback for a specific slave address
void registerResponseCallback(uint8_t slaveAddress, ResponseCallbackFn callback);

// Unregister a response callback
void unregisterResponseCallback(uint8_t slaveAddress);
```

This allows applications to be notified when response data is available without having to poll using `receiveSlaveResponse`:

```cpp
// Register a callback for slave address 5
driver.registerResponseCallback(5, [](uint8_t slaveAddress, const std::vector<uint8_t>& data) {
    std::cout << "Received data from slave " << (int)slaveAddress << std::endl;
    // Process the data
});

// Request power cycle count
status = driver.requestData(5, 0x41303031);  // "A001" in ASCII

// The callback will be invoked when the response is received
// No need to call receiveSlaveResponse unless you want to check for additional data
```

### 4.7 Slave Response API

#### 4.7.1 Overview

The Slave Response API enables PC applications to receive data sent by slave devices, working in conjunction with the Master Request API to provide a complete communication solution. This API corresponds to the "Slave Response API" category in the ZES driver API classification and includes buffer management to handle the slow nature of serial communication.

```cpp
RS485Error receiveSlaveResponse(uint8_t slaveAddress, std::vector<uint8_t>& responseData, bool waitForData = false, uint32_t timeout = 100)
```

**Purpose:** Receive data from slave devices

**Parameters:**
- `slaveAddress`: Address of the slave device (1-31)
- `responseData`: Reference to a vector that will be filled with the response data
- `waitForData`: If true, wait for data to be available; if false, return immediately if no data is available
- `timeout`: Maximum time to wait for data in milliseconds (only used if waitForData is true)

**Return Value:** `RS485Error::SUCCESS` if successful, otherwise an error code

**Key Features:**
1. **FIFO Buffer System**: Prevents data loss due to application layer delays or transmission delays
2. **Configurable Buffer Size**: Default 256KB, configurable up to 512KB
3. **Data Ready Notification**: Flag system notifies the application when data is available
4. **Dual Operation Modes**: Both blocking and non-blocking operation supported

**Implementation Notes:**
- The driver automatically handles buffer management, including overflow protection
- The application should regularly check for data availability to prevent buffer overflow
- Serial transmission is slow (16 bytes may take 100-200ms), making buffer management critical

**Response Handling Approaches:**

The API supports two main approaches for handling responses, giving developers flexibility based on their application needs:

1. **Callback-Based Approach** (Event-Driven):
   ```cpp
   // Register a callback for event-driven response handling
   driver.registerResponseCallback(5, [](uint8_t addr, const std::vector<uint8_t>& data) {
       std::cout << "Data received from device " << (int)addr << ": "
                 << std::string(data.begin(), data.end()) << std::endl;
       // Process data immediately when it arrives
   });

   // Request data - the callback will be invoked when response arrives
   driver.requestData(5, 0x41303031);  // A001: SEL event log

   // Application can continue with other tasks
   // No need to explicitly call receiveSlaveResponse
   ```

   **Best for**: Real-time monitoring, GUI applications, or any scenario where you want to process data as soon as it arrives without blocking the main thread.

   **Enhanced Callback Example:**
   ```cpp
   // Register a callback for real-time response handling
   driver.registerResponseCallback(5, [](uint8_t addr, const std::vector<uint8_t>& data) {
       std::cout << "Real-time response from " << (int)addr << ": "
                 << std::string(data.begin(), data.end()) << std::endl;
       // Process data immediately when it arrives
   });
   driver.requestData(5, 0x41303031); // A001: SEL event log
   // Application continues with other tasks while callback handles responses
   ```

2. **Polling-Based Approach** (Sequential):
   ```cpp
   // Request data
   driver.requestData(5, 0x41303031);  // A001: SEL event log

   // Poll for response with timeout
   std::vector<uint8_t> responseData;
   RS485Error status;
   int attempts = 0;

   do {
       status = driver.receiveSlaveResponse(5, responseData, false);
       if (status == RS485Error::TIMEOUT_ERROR) {
           std::this_thread::sleep_for(std::chrono::milliseconds(50));
           attempts++;
       }
   } while (status == RS485Error::TIMEOUT_ERROR && attempts < 10);

   // Process data after it's received
   if (status == RS485Error::SUCCESS) {
       std::cout << "Data received: " << std::string(responseData.begin(), responseData.end()) << std::endl;
   }
   ```

   **Best for**: Sequential workflows, simple scripts, or when you need to ensure data is processed in a specific order.

   **Enhanced Polling Example:**
   ```cpp
   // Request data
   driver.requestData(5, 0x41303031); // A001: SEL event log

   // Poll for response with enhanced error handling
   std::vector<uint8_t> response;
   for (int i = 0; i < 5; i++) {
       RS485Error status = driver.receiveSlaveResponse(5, response, false);
       if (status == RS485Error::SUCCESS) {
           std::cout << "Polled data: " << std::string(response.begin(), response.end()) << std::endl;
           break;
       } else if (status == RS485Error::TIMEOUT_ERROR) {
           std::this_thread::sleep_for(std::chrono::milliseconds(50));
       } else {
           std::cerr << "Polling error: " << driver.getErrorString(status) << std::endl;
           break;
       }
   }
   ```

   **Guidance:** Use callbacks for real-time applications (e.g., GUI monitoring). Use polling for sequential workflows (e.g., scripted operations).

3. **Blocking Approach** (Not Recommended for Airborne Systems):
   ```cpp
   // Request data
   driver.requestData(5, 0x41303031);  // A001: SEL event log

   // Wait for response with timeout (blocks until data arrives or timeout)
   std::vector<uint8_t> responseData;
   status = driver.receiveSlaveResponse(5, responseData, true, 200);  // Wait up to 200ms

   // Process data after it's received
   if (status == RS485Error::SUCCESS) {
       std::cout << "Data received: " << std::string(responseData.begin(), responseData.end()) << std::endl;
   }
   ```

   **Note**: This approach is not recommended for airborne environments where the host system must process multiple tasks concurrently. The blocking approach can cause the application thread to become unresponsive while waiting for data, which may interfere with other critical operations.

#### 4.7.2 Buffer Configuration and Management

The driver provides comprehensive buffer management capabilities to handle the slow nature of serial communication. The buffer is critical for airborne environments where the host system must process multiple tasks concurrently, as it prevents data loss during periods when the application cannot immediately process incoming data.

##### ******* Buffer Configuration

The buffer size is configured during driver initialization:

```cpp
// Create a driver instance with default 256KB buffer
AI_SLDAP_RS485_Driver driver("COM3");

// Create a driver instance with larger 512KB buffer
AI_SLDAP_RS485_Driver driver("COM3", 9600, 100, 512 * 1024);
```

**Buffer Size Selection Guidelines:**

| Buffer Size | Use Case | Considerations |
|-------------|----------|----------------|
| 256KB (Default) | Typical applications | Balances memory use and data volume |
| 512KB | High data volume or slow polling | Prevents overflow in busy systems |

**Buffer Size Selection Criteria:**
Choose buffer size based on expected data rate (e.g., A001 JSON responses), polling frequency, and available memory. For airborne systems, 512KB is recommended for safety.

The buffer size should be chosen based on:
- Expected data volume (JSON responses from A001/A004 can be large)
- Frequency of response checking (less frequent checking requires larger buffer)
- Available system memory constraints
- Response time requirements
- System load characteristics (airborne systems with multiple concurrent tasks)

For airborne systems, the larger buffer provides additional safety margin to prevent data loss during periods of high system load.

##### ******* Buffer Management API

The driver provides a comprehensive API for buffer management:

```cpp
// Get buffer size
uint32_t getBufferSize() const;

// Get current buffer usage
uint32_t getBufferUsage() const;

// Get number of pending responses for a specific slave
uint32_t getPendingResponseCount(uint8_t slaveAddress) const;

// Clear buffer for all slaves
RS485Error clearBuffer();

// Clear buffer for a specific slave
RS485Error clearBuffer(uint8_t slaveAddress);

// Set buffer threshold for notification
RS485Error setBufferThreshold(uint32_t thresholdBytes, bool enableCallback = true);

// Register buffer threshold callback
using BufferThresholdCallbackFn = std::function<void(uint32_t currentUsage, uint32_t totalSize)>;
void registerBufferThresholdCallback(BufferThresholdCallbackFn callback);

// Set buffer overflow policy
enum class BufferOverflowPolicy {
    DISCARD_OLDEST,  // Discard oldest data when buffer is full (default)
    DISCARD_NEWEST,  // Discard new data when buffer is full
    TRIGGER_ERROR    // Return error when buffer is full
};
RS485Error setBufferOverflowPolicy(BufferOverflowPolicy policy);
```

**Buffer Management Example:**
```cpp
// Set buffer threshold to 80% of total size
driver.setBufferThreshold(driver.getBufferSize() * 0.8);

// Register buffer threshold callback
driver.registerBufferThresholdCallback([](uint32_t currentUsage, uint32_t totalSize) {
    std::cout << "Buffer usage high: " << currentUsage << " of " << totalSize
              << " bytes (" << (float)currentUsage / totalSize * 100.0f << "%)" << std::endl;
    // Take action to process data more quickly or clear buffer
});

// Set buffer overflow policy
driver.setBufferOverflowPolicy(BufferOverflowPolicy::DISCARD_OLDEST);
```

**FIFO Buffer System:**

The driver implements a First-In-First-Out (FIFO) buffer system with the following characteristics:
- Data is stored in the order it is received
- A flag system notifies the application layer when data is ready to be retrieved
- Buffer overflow protection prevents data loss according to the configured policy
- Thread-safe implementation allows concurrent access from multiple threads

#### 4.7.3 Thread Safety

The Slave Response API is designed to be thread-safe, allowing multiple threads to call `receiveSlaveResponse` concurrently. This is particularly useful in applications with a dedicated thread for handling responses.

**Thread Safety Notes:**
- `receiveSlaveResponse` is thread-safe and can be called from multiple threads
- Buffer management methods are also thread-safe
- The driver uses internal synchronization to prevent race conditions
- Callbacks are invoked on a separate thread, so callback handlers should be thread-safe

**Thread Safety Best Practices:**
For multi-threaded applications, ensure callbacks are thread-safe. Example: Use a mutex in the callback if accessing shared resources.

```cpp
std::mutex logMutex;
driver.registerResponseCallback(5, [&logMutex](uint8_t addr, const std::vector<uint8_t>& data) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::cout << "Thread-safe response: " << std::string(data.begin(), data.end()) << std::endl;
    // Safe to access shared resources here
});
```

#### 4.7.4 Relationship with Master Request API

The Slave Response API works in direct conjunction with the Master Request API, forming a complete non-blocking communication system that is critical for airborne environments:

1. **Request-Response Pairing**: Each call to `requestData()` typically requires a corresponding call to `receiveSlaveResponse()` to retrieve the actual data
2. **Non-Blocking Design**: The Master Request API initiates the request and returns immediately, allowing the host thread to continue processing other tasks
3. **Asynchronous Operation**: After the request is sent, the driver handles the communication in the background using OS threads
4. **Data Ready Notification**: When data is received, it is stored in the buffer and a flag is set to indicate data is available
5. **Buffer Management**: The Slave Response API manages the buffer where responses are stored after being requested by the Master Request API
6. **Callback Alternative**: The callback mechanism can be used instead of explicit calls to `receiveSlaveResponse()` for a more event-driven approach

This separation of concerns allows for more flexible application design, especially for airborne systems where the host must process multiple tasks concurrently. The non-blocking design ensures that slow serial communication does not interfere with other critical operations.

**Workflow Diagram:**

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Application     │     │ Driver          │     │ Slave Device    │
│ Thread          │     │ (Background)    │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │ requestData()         │                       │
         │───────────────────────>                       │
         │                       │                       │
         │ Returns immediately   │                       │
         │<───────────────────────                       │
         │                       │                       │
         │                       │ Send request          │
         │                       │───────────────────────>
         │                       │                       │
         │                       │ Process request       │
         │                       │                       │
         │                       │ Send response         │
         │                       │<───────────────────────
         │                       │                       │
         │                       │ Store in buffer       │
         │                       │                       │
         │ receiveSlaveResponse()│                       │
         │───────────────────────>                       │
         │                       │                       │
         │ Return data from buffer                       │
         │<───────────────────────                       │
         │                       │                       │
         │ Process data          │                       │
         │                       │                       │
```

This design is particularly important for airborne environments where the host system must remain responsive to handle multiple concurrent tasks.

## 5. Using the API

### 5.1 Supported Operating Systems

- Windows (7, 8, 10, 11)
- Linux (Ubuntu 18.04+, CentOS 7+)

### 5.2 Installation

#### 5.2.1 FTDI VCP Driver Installation

Before using the API, you must install the FTDI VCP drivers:

1. Download the appropriate driver from https://ftdichip.com/drivers/vcp-drivers/
2. Install according to the instructions for your operating system
3. Connect the USB-RS485-WE-1800-BT adapter to your computer
4. Verify the device appears as a virtual COM port in your system

#### 5.2.2 ZES Driver Installation Options

The ZES middleware driver can be installed using one of two approaches:

**Option 1: OS I/O Thread Integration**
- ZES driver is installed as a device driver
- Takes position/registration of FTDI's driver
- Requires technical method to include FTDI's driver as part of ZES driver
- Provides seamless integration with OS-level I/O operations

**Option 2: Independent Thread Operation**
- ZES driver runs as an independent background task
- Registered as a background service under the respective OS
- Starts automatically when OS boots
- Provides better isolation and easier maintenance

**Recommended Approach:** Option 2 (Independent Thread) is recommended for most applications as it provides better stability and easier troubleshooting.

#### 5.2.3 ZES Driver Requirements

The ZES middleware driver provides the following core functionalities as specified in the protocol:

1. **Data Link Layer Implementation**:
   - Data frame packing and unpacking (16-byte frame format)
   - CRC8 calculation and verification
   - Header (0xAA) and Trailer (0x0D) handling

2. **RS485 Bus Control and Configuration**:
   - Slave address management (S001 command handling)
   - Baud rate management (S002 command handling)
   - Bus timing and collision avoidance

3. **Transmission Error Handling**:
   - CRC error detection and frame re-send
   - Timeout error detection and frame re-send
   - Automatic retry mechanisms with configurable limits

4. **Buffer and FIFO Functions**:
   - Configurable buffer sizes (256KB/512KB)
   - FIFO data management to avoid data loss
   - Buffer overflow protection with configurable policies

5. **API Interface**:
   - Five-category API structure as defined in this document
   - Thread-safe operation for concurrent access
   - Non-blocking design for airborne environments

### 5.3 Windows Driver Quick Start Guide

This section provides a step-by-step guide to get started with the Windows Driver Kit (WDK) based RS485 driver API:

#### 5.3.1 Windows Driver Setup and Device Discovery

```cpp
#include "RS485DriverInterface.h"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>

int main() {
    // Step 1: Enumerate available devices through Windows Device Manager
    std::vector<DeviceInfo> devices;
    RS485Error result = AI_SLDAP_RS485_DriverInterface::enumerateDevices(devices);

    if (result != RS485Error::SUCCESS || devices.empty()) {
        std::wcerr << L"No RS485 devices found in Windows Device Manager. Please check connections and driver installation." << std::endl;
        return 1;
    }

    // Display available devices with enhanced information
    std::wcout << L"Found " << devices.size() << L" RS485 devices:" << std::endl;
    for (size_t i = 0; i < devices.size(); i++) {
        std::wcout << i + 1 << L". Device Path: " << std::wstring(devices[i].port.begin(), devices[i].port.end()) << std::endl;
        std::wcout << L"   Description: " << std::wstring(devices[i].description.begin(), devices[i].description.end()) << std::endl;
        std::wcout << L"   Driver Version: " << std::wstring(devices[i].driverVersion.begin(), devices[i].driverVersion.end()) << std::endl;
        std::wcout << L"   Driver Loaded: " << (devices[i].isDriverLoaded ? L"Yes" : L"No") << std::endl;
        std::wcout << L"   VID:PID: " << std::hex << devices[i].vendorId << L":" << devices[i].productId << std::dec << std::endl;
        std::wcout << L"   ---" << std::endl;
    }

    // Step 2: Connect to the first device using Windows driver interface
    AI_SLDAP_RS485_DriverInterface driver;
    std::wstring devicePath = std::wstring(devices[0].port.begin(), devices[0].port.end());
    result = driver.open(devicePath);

    if (result != RS485Error::SUCCESS) {
        std::wcerr << L"Failed to open driver connection: "
                   << driver.getErrorString(result) << std::endl;
        return 1;
    }

    std::wcout << L"Successfully connected to Windows driver: " << devicePath << std::endl;

    // Step 3: Check driver-managed buffer status
    BufferStatus bufferStatus;
    result = driver.getBufferStatus(bufferStatus);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"Driver Buffer Status:" << std::endl;
        std::wcout << L"  Uplink: " << bufferStatus.uplinkUsed << L"/" << bufferStatus.uplinkTotal
                   << L" frames (" << bufferStatus.uplinkUsed * bufferStatus.frameSize << L" bytes)" << std::endl;
        std::wcout << L"  Downlink: " << bufferStatus.downlinkUsed << L"/" << bufferStatus.downlinkTotal
                   << L" frames (" << bufferStatus.downlinkUsed * bufferStatus.frameSize << L" bytes)" << std::endl;
        std::wcout << L"  Frame size: " << bufferStatus.frameSize << L" bytes" << std::endl;
        std::wcout << L"  Total buffer capacity: " <<
                      (bufferStatus.uplinkTotal + bufferStatus.downlinkTotal) * bufferStatus.frameSize << L" bytes" << std::endl;

        if (bufferStatus.isOverflowDetected) {
            std::wcout << L"  Warning: Buffer overflow detected!" << std::endl;
        }
    }

    // Step 4: Set up error handling with error categorization
    driver.registerErrorCallback([](RS485Error error, const char* message) {
        // Check if error is transient or permanent
        bool isTransient = (error == RS485Error::TIMEOUT_ERROR ||
                           error == RS485Error::CRC_ERROR ||
                           error == RS485Error::DEVICE_BUSY);

        if (isTransient) {
            std::wcerr << L"Transient error occurred: " << message
                       << L" (may resolve with retry)" << std::endl;
        } else {
            std::wcerr << L"Permanent error occurred: " << message
                       << L" (requires user intervention)" << std::endl;
        }
    });

    // Step 5: Configure buffer overflow policy
    result = driver.setBufferOverflowPolicy(BufferOverflowPolicy::DISCARD_OLDEST);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"Buffer overflow policy set to discard oldest frames" << std::endl;
    }

    // Step 6: Configure a slave device (using broadcasting)
    // Note: Only one slave device should be connected
    result = driver.configureSystemSettings(0x53303031, 5);  // S001: Set slave address to 5

    if (result != RS485Error::SUCCESS) {
        std::wcerr << L"Failed to set slave address: " << driver.getErrorString(result) << std::endl;
        driver.close();
        return 1;
    }

    // Step 7: Configure user settings (using the slave address we just set)
    result = driver.configureUserSettings(0x55303031, 250);  // U001: SEL detection threshold

    // Step 8: Monitor buffer status after configuration
    result = driver.getBufferStatus(bufferStatus);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"Buffer status after configuration:" << std::endl;
        std::wcout << L"  Uplink used: " << bufferStatus.uplinkUsed << L" frames" << std::endl;
        std::wcout << L"  Downlink used: " << bufferStatus.downlinkUsed << L" frames" << std::endl;
    }

    // Step 9: Request data with callback (non-blocking approach)
    driver.registerResponseCallback(5, [](uint8_t addr, const std::vector<uint8_t>& data) {
        std::wcout << L"Data received from device " << (int)addr << L": "
                   << std::wstring(data.begin(), data.end()) << std::endl;
    });

    result = driver.requestData(5, 0x41303033);  // A003: Request firmware version

    // Wait a bit for the callback to be invoked
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // Step 10: Request data with polling (alternative approach)
    result = driver.requestData(5, 0x41303032);  // A002: Request device status

    std::vector<uint8_t> responseData;
    int attempts = 0;

    do {
        result = driver.receiveSlaveResponse(5, responseData, false);
        if (result == RS485Error::TIMEOUT_ERROR) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            attempts++;
        }
    } while (result == RS485Error::TIMEOUT_ERROR && attempts < 10);

    if (result == RS485Error::SUCCESS) {
        // Process the device status data
        if (responseData.size() >= 2) {
            uint16_t statusFlags = (responseData[0] << 8) | responseData[1];
            std::wcout << L"Device status flags: 0x" << std::hex << statusFlags << std::dec << std::endl;
        }
    }

    // Step 11: Final buffer status check
    result = driver.getBufferStatus(bufferStatus);
    if (result == RS485Error::SUCCESS) {
        std::wcout << L"Final buffer status:" << std::endl;
        std::wcout << L"  Uplink used: " << bufferStatus.uplinkUsed << L"/" << bufferStatus.uplinkTotal << L" frames" << std::endl;
        std::wcout << L"  Downlink used: " << bufferStatus.downlinkUsed << L"/" << bufferStatus.downlinkTotal << L" frames" << std::endl;

        if (bufferStatus.isOverflowDetected) {
            std::wcout << L"  Warning: Buffer overflow occurred during operation!" << std::endl;
        }
    }

    // Step 12: Clean up and close
    driver.unregisterResponseCallback(5);
    driver.close();
    std::wcout << L"Windows driver connection closed successfully" << std::endl;
    return 0;
}
```

#### 5.3.2 Complete End-to-End Example

This example demonstrates the complete workflow from device discovery to configuration and data retrieval:

```cpp
#include "AI_SLDAP_RS485_Driver.h"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>

int main() {
    // Step 1: Enumerate and connect to device
    std::vector<DeviceInfo> devices;
    if (AI_SLDAP_RS485_Driver::enumerateDevices(devices) != RS485Error::SUCCESS || devices.empty()) {
        std::cerr << "No devices found" << std::endl;
        return 1;
    }

    AI_SLDAP_RS485_Driver driver(devices[0].port);
    if (driver.open() != RS485Error::SUCCESS) {
        std::cerr << "Failed to connect" << std::endl;
        return 1;
    }

    // Step 2: Configure slave address using S001 (Master Broadcasting API)
    // Note: Only one slave device should be connected when using broadcasting
    RS485Error status = driver.configureSystemSettings(0x53303031, 5);  // Set slave address to 5
    if (status != RS485Error::SUCCESS) {
        std::cerr << "Failed to set slave address: " << driver.getErrorString(status) << std::endl;
        driver.close();
        return 1;
    }

    // Step 3: Configure device parameters using U-series commands (Master Assign Data API)
    // These commands will be sent to the slave address we just set (5)
    status = driver.configureUserSettings(0x55303031, 250);  // U001: SEL detection threshold
    status = driver.configureUserSettings(0x55303032, 1500); // U002: Max amplitude threshold
    status = driver.configureUserSettings(0x55303033, 3);    // U003: SEL detection count
    status = driver.configureUserSettings(0x55303034, 600);  // U004: Power cycle duration

    // Step 4: Verify configuration
    bool isMatching;
    status = driver.verifySystemConfig(0x55303031, 250, isMatching);
    if (status == RS485Error::SUCCESS && isMatching) {
        std::cout << "Configuration verified successfully" << std::endl;
    }

    // Step 5: Request data using A-series commands (Master Request API)
    // Register a callback for responses
    driver.registerResponseCallback(5, [](uint8_t slaveAddress, const std::vector<uint8_t>& data) {
        std::cout << "Received data from slave " << (int)slaveAddress << std::endl;
        // Process the data based on the request type
        // For this example, we'll assume it's power cycle count (A001)
        if (data.size() >= 4) {
            uint32_t powerCycleCount = 0;
            for (int i = 0; i < 4; i++) {
                powerCycleCount = (powerCycleCount << 8) | data[i];
            }
            std::cout << "Power cycle count: " << powerCycleCount << std::endl;
        }
    });

    // Request SEL event log
    status = driver.requestData(5, 0x41303031);  // A001: SEL event log
    if (status != RS485Error::SUCCESS) {
        std::cerr << "Failed to request data: " << driver.getErrorString(status) << std::endl;
    }

    // Wait a bit for the response to be processed by the callback
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Step 6: Alternative way to retrieve data without callbacks
    std::vector<uint8_t> responseData;

    // Request with filtering options
    RequestOptions options;
    options.startTime = 1620000000;  // Only events after this time
    options.maxRecords = 5;          // Limit to 5 records
    status = driver.requestData(5, 0x41303031, &options);  // A001 with filtering

    // Wait for and retrieve the response
    status = driver.receiveSlaveResponse(5, responseData, true, 200);
    if (status == RS485Error::SUCCESS && !responseData.empty()) {
        std::cout << "SEL event log received, processing..." << std::endl;
        // Process JSON response data
        std::string jsonStr(responseData.begin(), responseData.end());
        std::cout << "Event log: " << jsonStr << std::endl;
    }

    // Request device status
    status = driver.requestData(5, 0x41303032);  // A002: Device status
    status = driver.receiveSlaveResponse(5, responseData, true, 200);
    if (status == RS485Error::SUCCESS && responseData.size() >= 2) {
        uint16_t statusFlags = (responseData[0] << 8) | responseData[1];
        std::cout << "Device status flags: 0x" << std::hex << statusFlags << std::dec << std::endl;
    }

    // Request current configuration (A005) - NEW COMMAND
    status = driver.requestData(5, 0x41303035);  // A005: Current configuration
    status = driver.receiveSlaveResponse(5, responseData, true, 200);
    if (status == RS485Error::SUCCESS && !responseData.empty()) {
        std::string configJson(responseData.begin(), responseData.end());
        std::cout << "Current configuration: " << configJson << std::endl;
        // This shows all current settings stored in FRAM
    }

    // Step 7: Write model data (W-series commands)
    std::vector<uint8_t> modelData = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    status = driver.modelDataOperation(5, 0x1000, modelData, true);

    // Read back the model data to verify
    std::vector<uint8_t> readData;
    status = driver.modelDataOperation(5, 0x1000, readData, false, 8);

    // Step 8: Clean up and close
    driver.unregisterResponseCallback(5);
    driver.close();

    return 0;
}
```

### 5.3 API Usage Examples

#### 5.3.1 Complete Example Using All Three API Categories

```cpp
#include "AI_SLDAP_RS485_Driver.h"
#include <iostream>
#include <vector>
#include <iomanip>
#include <thread>
#include <chrono>

int main() {
    // 1. Enumerate devices
    std::vector<DeviceInfo> devices;
    RS485Error result = AI_SLDAP_RS485_Driver::enumerateDevices(devices);
    if (result != RS485Error::SUCCESS || devices.empty()) {
        std::cerr << "No devices found" << std::endl;
        return 1;
    }

    // 2. Connect to the first device
    AI_SLDAP_RS485_Driver driver(devices[0].port);
    result = driver.open();
    if (result != RS485Error::SUCCESS) {
        std::cerr << "Failed to connect: " << driver.getErrorString(result) << std::endl;
        return 1;
    }

    // Register error callback
    driver.registerErrorCallback([](RS485Error error, const char* message) {
        std::cerr << "Error occurred: " << message << std::endl;
    });

    // 3. Category 1: System Configuration API (Master Broadcasting API)
    // Set slave address (S001) - Note: Only one slave device should be connected
    result = driver.configureSystemSettings(0x53303031, 1);  // "S001" in ASCII
    if (result != RS485Error::SUCCESS) {
        std::cerr << "Failed to set slave address: " << driver.getErrorString(result) << std::endl;
        driver.close();
        return 1;
    }

    // 4. Category 2: Master Assign Data API (U-series commands)
    // These commands will be sent to the slave address we just set (1)
    result = driver.configureUserSettings(0x55303031, 250);  // U001: SEL detection threshold
    result = driver.configureUserSettings(0x55303032, 1500); // U002: Max amplitude threshold
    result = driver.configureUserSettings(0x55303033, 3);    // U003: SEL detection count
    result = driver.configureUserSettings(0x55303034, 600);  // U004: Power cycle duration

    // Verify configuration
    bool isMatching;
    result = driver.verifySystemConfig(0x55303031, 250, isMatching);
    if (result == RS485Error::SUCCESS && isMatching) {
        std::cout << "SEL detection threshold is correctly set to 250mA" << std::endl;
    }

    // 5. Category 3: Master Request API (A-series commands)
    // Register a callback for responses
    driver.registerResponseCallback(1, [](uint8_t slaveAddress, const std::vector<uint8_t>& data) {
        std::cout << "Received data from slave " << (int)slaveAddress << std::endl;
        // Process the data based on the request type (assuming JSON format for A001)
        std::string jsonStr(data.begin(), data.end());
        std::cout << "Received data: " << jsonStr << std::endl;

        // In a real application, you would parse the JSON here
        // Example: auto json = JSON::parse(jsonStr);
    });

    // Request SEL event log
    result = driver.requestData(1, 0x41303031);  // A001: SEL event log

    // Wait a bit for the response to be processed by the callback
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // Request with filtering options
    RequestOptions options;
    options.startTime = 1620000000;  // Only events after this time
    options.maxRecords = 5;          // Limit to 5 records
    options.eventTypeFilter = 0x0003; // Only certain event types
    result = driver.requestData(1, 0x41303031, &options);  // A001 with filtering

    // Use Category 4: Slave Response API to retrieve the response
    std::vector<uint8_t> responseData;
    result = driver.receiveSlaveResponse(1, responseData, true, 200);  // Wait up to 200ms
    if (result == RS485Error::SUCCESS && !responseData.empty()) {
        std::string jsonStr(responseData.begin(), responseData.end());
        std::cout << "Filtered SEL events: " << jsonStr << std::endl;
    }

    // Request device status (binary format)
    result = driver.requestData(1, 0x41303032);  // A002: Device status
    result = driver.receiveSlaveResponse(1, responseData, true, 200);
    if (result == RS485Error::SUCCESS && responseData.size() >= 2) {
        uint16_t statusFlags = (responseData[0] << 8) | responseData[1];
        std::cout << "Device status: 0x" << std::hex << statusFlags << std::dec << std::endl;
        // Interpret status flags
        bool isSELDetected = (statusFlags & 0x0001) != 0;
        bool isPowerCycleActive = (statusFlags & 0x0002) != 0;
        std::cout << "SEL detected: " << (isSELDetected ? "Yes" : "No") << std::endl;
        std::cout << "Power cycle active: " << (isPowerCycleActive ? "Yes" : "No") << std::endl;
    }

    // Request firmware version (string format)
    result = driver.requestData(1, 0x41303033);  // A003: Firmware version
    result = driver.receiveSlaveResponse(1, responseData, true, 200);
    if (result == RS485Error::SUCCESS && !responseData.empty()) {
        std::string version(responseData.begin(), responseData.end());
        std::cout << "Firmware version: " << version << std::endl;
    }

    // Request system statistics (JSON format)
    result = driver.requestData(1, 0x41303034);  // A004: System statistics
    result = driver.receiveSlaveResponse(1, responseData, true, 200);
    if (result == RS485Error::SUCCESS && !responseData.empty()) {
        std::string jsonStr(responseData.begin(), responseData.end());
        std::cout << "System statistics: " << jsonStr << std::endl;
    }

    // Request current configuration (JSON format) - NEW COMMAND
    result = driver.requestData(1, 0x41303035);  // A005: Current configuration
    result = driver.receiveSlaveResponse(1, responseData, true, 200);
    if (result == RS485Error::SUCCESS && !responseData.empty()) {
        std::string configJson(responseData.begin(), responseData.end());
        std::cout << "Current configuration: " << configJson << std::endl;
        // This shows all current settings stored in FRAM, useful for verification
    }

    // Check buffer usage and management
    uint32_t bufferSize = driver.getBufferSize();
    uint32_t bufferUsage = driver.getBufferUsage();
    uint32_t pendingResponses = driver.getPendingResponseCount(1);

    std::cout << "Buffer usage: " << bufferUsage << " bytes ("
              << (float)bufferUsage / bufferSize * 100.0f << "% of "
              << bufferSize << " bytes)" << std::endl;
    std::cout << "Pending responses from slave 1: " << pendingResponses << std::endl;

    // Set buffer threshold and callback
    driver.setBufferThreshold(bufferSize * 0.8);  // 80% threshold
    driver.registerBufferThresholdCallback([](uint32_t currentUsage, uint32_t totalSize) {
        std::cout << "Buffer usage high: " << currentUsage << " of " << totalSize
                  << " bytes (" << (float)currentUsage / totalSize * 100.0f << "%)" << std::endl;
    });

    // 6. Category 5: AI Model Weight and Bias Data API (W-series commands)
    // Write model data
    std::vector<uint8_t> modelData = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    result = driver.modelDataOperation(1, 0x1000, modelData, true);

    // Read model data
    std::vector<uint8_t> readData;
    result = driver.modelDataOperation(1, 0x1000, readData, false, 8);

    if (result == RS485Error::SUCCESS && readData.size() == 8) {
        std::cout << "Read model data: ";
        for (uint8_t byte : readData) {
            std::cout << std::hex << std::setw(2) << std::setfill('0')
                      << (int)byte << " ";
        }
        std::cout << std::dec << std::endl;
    }

    // 7. Clean up and close
    driver.unregisterResponseCallback(1);
    driver.unregisterErrorCallback();
    driver.close();

    return 0;
}
```

#### 5.3.2 Error Handling

```cpp
// Error handling example
void handleApiError(RS485Error result, AI_SLDAP_RS485_Driver& driver) {
    switch (result) {
        case RS485Error::SUCCESS:
            // No error
            break;
        case RS485Error::CONNECTION_ERROR:
            std::cerr << "Connection error: Check hardware connections" << std::endl;
            break;
        case RS485Error::TIMEOUT_ERROR:
            std::cerr << "Timeout error: Device not responding" << std::endl;
            break;
        case RS485Error::INVALID_PARAMETER:
            std::cerr << "Invalid parameter value" << std::endl;
            break;
        default:
            std::cerr << "Error: " << driver.getErrorString(result) << std::endl;
            break;
    }
}

// Example usage
result = driver.configureSystem(0x55303031, 600);  // Invalid: outside 40-500 range
if (result != RS485Error::SUCCESS) {
    handleApiError(result, driver);
    // Try with valid value
    result = driver.configureSystem(0x55303031, 250);
}
```

## 6. Additional Features

### 6.1 Performance Metrics and Optimization

The API provides performance monitoring capabilities to help developers optimize their applications:

```cpp
// Performance metrics structure
struct PerformanceMetrics {
    double avgLatencyMs;        // Average response latency in milliseconds
    uint32_t bytesPerSecond;    // Throughput in bytes per second
    uint32_t successfulFrames;  // Number of successful frame transmissions
    uint32_t failedFrames;      // Number of failed frame transmissions
    uint32_t retryCount;        // Total number of retries performed
    double frameSuccessRate;    // Success rate as percentage (0-100)
};

// Get performance metrics
RS485Error getPerformanceMetrics(PerformanceMetrics& metrics);
```

**Performance Monitoring Example:**
```cpp
PerformanceMetrics metrics;
RS485Error status = driver.getPerformanceMetrics(metrics);
if (status == RS485Error::SUCCESS) {
    std::cout << "Avg latency: " << metrics.avgLatencyMs << "ms" << std::endl;
    std::cout << "Throughput: " << metrics.bytesPerSecond << " B/s" << std::endl;
    std::cout << "Success rate: " << metrics.frameSuccessRate << "%" << std::endl;
}
```

**Performance Optimization Guidelines:**
- Benchmark with different baud rates (e.g., 115200 vs. 9600) and buffer sizes to optimize for your use case
- Monitor frame success rates to identify communication issues
- Use higher baud rates for shorter cable runs to improve throughput
- Consider using larger buffers for high-volume data transfers

### 6.2 Hardware Features

| Feature | Command | Description |
|---------|---------|-------------|
| **SEL Protection** | U002, U003 | Configurable amplitude threshold (1000-2000mA) and detection count (1-5) |
| **GPIO Inputs** | U005 | 2 channels that can pause power cycling when high (disabled by default) |
| **GPIO Outputs** | U006 | 2 channels that go high during power cycling (disabled by default) |

### 6.3 GUI Integration

The RS485 driver includes a GUI application for easy configuration:

- Visual interface for all system configuration parameters
- Real-time status monitoring
- Device discovery and connection management

## 7. Windows Driver Implementation Summary

### 7.1 Key Changes: Filter Driver Architecture

**Architecture Changes:**
1. **Driver Type**: Implemented as UMDF 2.0 **Filter Driver** (not replacing FTDI VCP)
2. **Driver Stack Position**: **Upper Filter** above FTDI VCP Function Driver
3. **Buffer Management**: Implemented driver-managed fixed-size buffers (5 uplink + 10 downlink frames, 16 bytes each)
4. **Protocol Processing**: Added RS485/ZES protocol intelligence on top of basic serial communication
5. **Communication Method**: IOCTL-based high-level API while leveraging FTDI VCP for hardware communication

**Technical Benefits:**
- **Leverages Existing Infrastructure**: Uses proven FTDI VCP drivers as foundation
- **Enhanced Functionality**: Adds RS485 protocol processing and intelligent buffering
- **Maintains Compatibility**: Existing FTDI driver infrastructure remains intact
- **Improved Debugging**: Visual Studio integration with WDK debugging tools
- **Professional Deployment**: Standard Windows filter driver installation and signing process

**Buffer Management Advantages:**
- **Fixed Memory Allocation**: Eliminates dynamic memory allocation overhead
- **Real-Time Performance**: Optimized for low-latency airborne environments
- **Thread-Safe Operations**: Built-in synchronization for concurrent access
- **Overflow Protection**: Configurable policies for buffer overflow handling

### 7.2 Application Deliverable

**Final Deliverable:**

**RS485_Communication_Application.exe**: A complete Windows application that provides:

1. **Integrated FTDI VCP Driver Functionality**: Built-in support for USB-RS485-WE-1800-BT converter without requiring separate FTDI driver installation
2. **ZES Protocol Implementation**: Complete implementation of the ZES proprietary data link layer protocol as specified in the guidance document
3. **User-Mode Driver Framework (UMDF 2)**: Windows-native driver implementation for reliable RS485 communication
4. **Advanced Buffer Management**: Driver-managed frame buffers with intelligent overflow handling
5. **High-Level API Interface**: Five categories of APIs that abstract away protocol complexity
6. **Error Handling and Recovery**: Comprehensive error management with automatic retry mechanisms
7. **Configuration Management**: Persistent storage of device configurations in FRAM
8. **Multi-Device Support**: Support for up to 30 slave devices on a single RS485 bus

**Architecture Overview:**
The application combines Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver functionality to create a single executable that handles all aspects of RS485 communication with AI-SLDAP devices. This approach eliminates the need for separate driver installations while providing enterprise-grade reliability and performance.
5. **Test Applications**: Comprehensive test suite for validation
6. **Documentation**: Updated implementation guides and API documentation

### 7.3 Migration Benefits Summary

**For Developers:**
- Simplified installation process (no third-party driver dependencies)
- Enhanced debugging capabilities with Visual Studio WDK integration
- Better error handling and diagnostics through Windows driver framework
- Professional driver signing and deployment process

**For End Users:**
- Automatic driver installation through Windows Update (when signed)
- Better system stability and reliability
- Native Windows Device Manager integration
- Improved performance and lower latency

**For System Integration:**
- Standardized Windows driver architecture
- Better compatibility with Windows security policies
- Enhanced support for enterprise deployment scenarios
- Future-proof design aligned with Windows driver development trends

## 9. Critical Technical Implementation Highlights

### 9.1 Non-Blocking Driver Operation: Never Hold User Threads

**Problem Statement:**
How does the driver ensure that user application threads are never blocked during RS485 operations, especially in real-time airborne environments?

**Solution Architecture:**

**1. Asynchronous I/O Completion Model**
```cpp
// User thread makes IOCTL call
NTSTATUS ProcessIOCTLRequest(IWDFIoRequest* pRequest) {
    // NEVER block here - immediately queue request and return
    NTSTATUS status = QueueRequestForAsyncProcessing(pRequest);

    if (NT_SUCCESS(status)) {
        // Return STATUS_PENDING - user thread continues immediately
        return STATUS_PENDING;
    }

    // Only return synchronously for immediate errors
    return status;
}

// Processing happens in driver-managed work items
void ProcessRequestAsync(IWDFIoRequest* pRequest) {
    // This runs in driver context, not user thread
    // Perform actual RS485 communication
    // Complete request when done
    pRequest->Complete(STATUS_SUCCESS);
}
```

**2. Driver-Managed Thread Pool**
- **Work Items**: All complex processing occurs in driver work items
- **DPC Context**: Frame parsing happens at DPC level for minimal latency
- **Completion Callbacks**: User applications are notified via completion callbacks
- **No Blocking Primitives**: Driver never uses blocking waits or sleeps

**3. Request Queuing Strategy**
```cpp
class RequestManager {
private:
    WDFQUEUE m_pendingRequests;     // Queue for user requests
    WDFWORKITEM m_processorWorkItems[4];  // 4 parallel processors

public:
    // User thread context - returns immediately
    NTSTATUS QueueRequest(IWDFIoRequest* pRequest) {
        WdfIoQueueAdd(m_pendingRequests, pRequest);
        WdfWorkItemEnqueue(m_processorWorkItems[GetNextProcessor()]);
        return STATUS_PENDING;  // User thread continues
    }

    // Driver work item context - processes requests asynchronously
    void ProcessQueuedRequests() {
        IWDFIoRequest* pRequest;
        while (WdfIoQueueRetrieveNextRequest(m_pendingRequests, &pRequest) == STATUS_SUCCESS) {
            ProcessSingleRequest(pRequest);
            pRequest->Complete(STATUS_SUCCESS);
        }
    }
};
```

**4. Benefits for Airborne Systems**
- **Real-Time Responsiveness**: User applications remain responsive
- **Deterministic Behavior**: No unpredictable blocking delays
- **Scalability**: Multiple applications can use driver simultaneously
- **System Stability**: No thread starvation or deadlock risks

### 9.2 Frame Processing: Efficient State Machine Implementation

**Problem Statement:**
How does the driver efficiently process incoming RS485 frames byte-by-byte without blocking and handle frame synchronization, CRC validation, and error recovery?

**Solution Architecture:**

**1. Multi-Level Processing Pipeline**
```
Level 1: DPC Context (Minimal Latency)
├── Byte-by-byte state machine
├── Frame boundary detection
└── Basic validation

Level 2: Work Item Context (Complex Processing)
├── CRC8 validation
├── Protocol interpretation
├── Buffer management
└── Error handling
```

**2. State Machine Implementation**
```cpp
// Called from DPC - processes one byte in microseconds
FrameProcessResult ProcessIncomingByte(uint8_t byte) {
    // State machine with minimal processing per byte
    switch (m_currentState) {
        case WAITING_HEADER:
            if (byte == 0xAA) {
                m_frameBuffer[0] = byte;
                m_bytesReceived = 1;
                m_currentState = READING_ID;
            }
            return CONTINUE;  // Never blocks

        case READING_PAYLOAD:
            m_frameBuffer[m_bytesReceived++] = byte;
            if (m_bytesReceived == 14) {
                m_currentState = READING_CRC;
            }
            return CONTINUE;  // Process next byte

        case READING_TRAILER:
            if (byte == 0x0D && m_bytesReceived == 16) {
                return FRAME_READY;  // Schedule work item
            } else {
                ResetStateMachine();
                return FRAME_ERROR;
            }
    }
}
```

**3. Frame Validation and Error Recovery**
```cpp
// Called from work item - can perform complex operations
void ProcessCompleteFrame(const RS485Frame& frame) {
    // CRC8 validation
    uint8_t calculatedCRC = CalculateCRC8(&frame.id_byte, 13);
    if (calculatedCRC != frame.crc8) {
        // Non-blocking error recovery
        ScheduleResendRequest(frame);
        return;
    }

    // Route to appropriate buffer based on function code
    uint8_t functionCode = (frame.id_byte >> 5) & 0x07;
    uint8_t deviceAddress = frame.id_byte & 0x1F;

    // Store in ring buffer and notify waiting applications
    StoreInDownlinkBuffer(frame, deviceAddress);
    NotifyWaitingApplication(deviceAddress);  // Async notification
}
```

**4. Performance Characteristics**
- **DPC Processing**: < 10 microseconds per byte
- **Frame Completion**: < 100 microseconds for complete frame
- **Memory Efficiency**: Fixed 16-byte frame buffer per state machine
- **Error Recovery**: Automatic retry without user intervention
- **Throughput**: Supports full RS485 bandwidth (up to 115200 baud)

**5. Synchronization Strategy**
```cpp
// Lock-free design for maximum performance
class FrameBuffer {
private:
    volatile LONG m_writeIndex;
    volatile LONG m_readIndex;
    RS485Frame m_frames[BUFFER_SIZE];

public:
    // Producer (DPC context) - lock-free
    bool PushFrame(const RS485Frame& frame) {
        LONG currentWrite = m_writeIndex;
        LONG nextWrite = (currentWrite + 1) % BUFFER_SIZE;

        if (nextWrite == m_readIndex) {
            return false;  // Buffer full
        }

        m_frames[currentWrite] = frame;
        InterlockedExchange(&m_writeIndex, nextWrite);
        return true;
    }

    // Consumer (user context) - lock-free
    bool PopFrame(RS485Frame& frame) {
        LONG currentRead = m_readIndex;
        if (currentRead == m_writeIndex) {
            return false;  // Buffer empty
        }

        frame = m_frames[currentRead];
        InterlockedExchange(&m_readIndex, (currentRead + 1) % BUFFER_SIZE);
        return true;
    }
};
```

**Key Technical Achievements:**
1. **Zero User Thread Blocking**: All operations return immediately or use async completion
2. **Efficient Frame Processing**: State machine processes frames with minimal CPU overhead
3. **Lock-Free Design**: Ring buffers use atomic operations instead of locks
4. **Automatic Error Recovery**: CRC errors and timeouts handled transparently
5. **Real-Time Performance**: Suitable for airborne and industrial control systems

## 8. Frequently Asked Questions (FAQ)

### 8.1 Windows Driver Questions

**Q: What are the system requirements for the Windows Driver Kit (WDK) based RS485 driver?**
A: The driver requires Windows 10 or later (64-bit), Visual Studio 2022 with WDK extension for development, and appropriate USB-RS485 converter hardware. The driver is digitally signed for production deployment.

**Q: How does the driver-managed buffer system work?**
A: The driver maintains fixed-size buffers: 5 frames (80 bytes) for uplink (PC to device) and 10 frames (160 bytes) for downlink (device to PC). Each frame is 16 bytes. The driver automatically manages these buffers with configurable overflow policies.

**Q: Can I use the old FTDI VCP driver alongside the new Windows filter driver?**
A: Yes! In fact, our RS485 filter driver **requires** the FTDI VCP driver to be present. The FTDI VCP driver serves as the Function Driver (底层) that handles the basic USB-to-serial conversion, while our RS485 filter driver sits above it as an Upper Filter Driver (上层) to provide RS485 protocol processing and advanced buffering.

**Q: How do I install the Windows driver?**
A: For development: Use Visual Studio's driver deployment features. For production: Install the signed driver package (.inf file) through Device Manager or use the provided installer. The driver will appear in Windows Device Manager under "Ports" or a custom device category.

**Q: What happens if the driver buffer overflows?**
A: The driver supports three overflow policies: DISCARD_OLDEST (default), DISCARD_NEWEST, or TRIGGER_ERROR. Applications can monitor buffer status and configure the policy based on their requirements.

**Q: How does the Filter Driver communicate with the FTDI VCP Function Driver?**
A: The RS485 Filter Driver sits in the Windows driver stack above the FTDI VCP Function Driver. When applications send IOCTL requests to our filter driver, it processes the RS485 protocol logic (frame packing, CRC calculation, etc.) and then forwards the processed serial data to the FTDI VCP driver using standard Windows I/O mechanisms (IRP forwarding). Similarly, data received from the FTDI driver is processed by our filter driver before being returned to the application.

**Q: How does the driver ensure it never blocks user application threads?**
A: The driver uses a sophisticated asynchronous I/O model where all IOCTL calls return immediately with STATUS_PENDING. Actual processing occurs in driver-managed work items and DPC contexts. User threads never wait for RS485 operations to complete - instead, they receive completion notifications through callbacks or can poll for results. This is critical for real-time airborne systems where thread blocking is unacceptable.

**Q: How does the driver process RS485 frames efficiently?**
A: The driver implements a multi-level frame processing pipeline: (1) DPC context handles byte-by-byte state machine processing in microseconds, detecting frame boundaries and basic validation; (2) Work item context performs complex operations like CRC8 validation, protocol interpretation, and buffer management. The state machine processes one byte at a time without blocking, using lock-free ring buffers for maximum performance. This design supports full RS485 bandwidth while maintaining real-time responsiveness.

### 8.2 General Questions

**Q: What is the default slave address used if no S001 command has been executed?**
A: The default slave address is 0x00 (broadcast address). However, it's recommended to always explicitly set the slave address using S001 before sending U-series commands.

**Q: Can I connect multiple slave devices when using the Master Broadcasting API?**
A: No. Due to hardware limitations, only one slave device should be connected when using the Master Broadcasting API (S-series commands). Multiple slaves responding simultaneously would cause bus collisions.

**Q: What if I get no response during broadcasting operations?**
A: If you're not getting responses during broadcasting:
1. Ensure only one slave device is connected to the RS485 bus
2. Check cable integrity and connections
3. Verify power to the slave device
4. Try a lower baud rate temporarily to rule out timing issues
5. Check termination resistors on the RS485 bus
6. Use the `detectMultipleDevices()` function to check for multiple devices

**Q: How do I handle communication errors?**
A: The driver provides comprehensive error handling through error codes and error callbacks. You can register an error callback to be notified of errors asynchronously, or check the return value of each API call.

**Q: How do I distinguish between transient and permanent errors?**
A: The driver categorizes errors into two types:
- **Transient errors**: May resolve with retries (e.g., TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)
- **Permanent errors**: Require user intervention (e.g., INVALID_PARAMETER, DEVICE_NOT_FOUND)

You can implement retry logic for transient errors:
```cpp
// Example retry logic
if (isTransientError(error)) {
    for (int i = 0; i < 3; i++) {
        // Wait and retry
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        status = driver.requestData(slaveAddress, dataKey);
        if (status == RS485Error::SUCCESS) break;
    }
}
```

See section 4.0.1 for a complete implementation example.

**Q: Is the driver thread-safe?**
A: Yes, the driver is designed to be thread-safe. You can call API functions from multiple threads without worrying about race conditions. The driver uses internal synchronization to prevent conflicts.

### 7.2 Configuration and Addressing

**Q: How do I configure multiple slave devices?**
A: To configure multiple slaves, you need to:
1. Set the slave address using S001 (e.g., `configureSystemSettings(0x53303031, 1)`)
2. Configure that slave using U-series commands via `configureUserSettings`
3. Change the slave address using S001 again (e.g., `configureSystemSettings(0x53303031, 2)`)
4. Configure the next slave using U-series commands via `configureUserSettings`
5. Repeat for each slave

**Q: Why are there separate functions `configureSystemSettings` and `configureUserSettings` instead of a single function?**
A: The separation provides clearer distinction between S-series commands (system settings via broadcasting) and U-series commands (user settings to specific slaves). This design makes the API more intuitive, prevents misuse, and better reflects the underlying protocol differences between these command types.

**Q: Do S001 and U001 commands need to be differentiated even if we only have one slave device?**
A: Yes, they should still be differentiated for several reasons:
1. S001 uses broadcasting (address 0x00) while U001 targets a specific slave address
2. S001 sets the addressing context for subsequent U-series commands
3. The separation maintains consistency with the ZES protocol design
4. It provides better code clarity and maintainability
5. It allows for future expansion if additional slave devices are added later

**Q: Why does the API use JSON format for A001 and A004 commands?**
A: JSON format is used for several industry-standard reasons:
1. **Flexibility**: JSON can represent complex nested data structures without requiring fixed schemas
2. **Self-describing**: The format is human-readable and self-documenting
3. **Extensibility**: New fields can be added without breaking backward compatibility
4. **Widespread Support**: JSON parsing libraries are available in virtually all programming languages
5. **Industry Standard**: Modern serial communication protocols increasingly use JSON for complex data
6. **Size Efficiency**: For complex data, JSON can be more compact than fixed binary formats with many optional fields

**Q: What is the purpose of the RequestOptions structure in the requestData API?**
A: The RequestOptions structure follows industry-standard patterns for serial communication APIs by providing:
1. **Parameterized Requests**: Allows filtering and limiting data without creating numerous specialized functions
2. **Pagination Support**: Enables retrieving large datasets in manageable chunks (offset + maxRecords)
3. **Time-Based Filtering**: Particularly useful for event logs to retrieve events within specific time ranges
4. **Type Filtering**: Allows requesting only specific types of events or data
5. **Format Control**: Provides options for controlling response format (e.g., compact JSON)
6. **Optional Usage**: The parameter is optional, maintaining backward compatibility and simplicity for basic requests
7. **Extensibility**: The structure can be extended with new fields without breaking existing code

### 7.3 Response Handling and Performance

**Q: When should I use callbacks versus polling for response handling?**
A: Choose based on your application needs:
- **Use callbacks when**:
  - Building event-driven applications (e.g., GUI interfaces)
  - Handling real-time monitoring where responses need immediate processing
  - Working with asynchronous workflows where you don't want to block execution

- **Use polling when**:
  - Implementing sequential workflows where order matters
  - Writing simpler scripts without callback complexity
  - Working in environments where threading is limited

- **Use blocking calls when**:
  - Writing the simplest possible code
  - In scripts where blocking is acceptable
  - When you need guaranteed sequential processing

See section 4.7.1 for implementation examples of each approach.

**Q: How do I prevent buffer overflow during long operations?**
A: You can:
1. Set a buffer threshold and register a callback to be notified when the buffer usage exceeds that threshold
2. Configure the buffer overflow policy to determine how overflow situations are handled
3. Regularly call `receiveSlaveResponse` to process data from the buffer
4. Use a dedicated thread for handling responses

**Q: What is the maximum transmission speed supported?**
A: The driver supports baud rates up to 115200 bps. However, the actual throughput may be lower due to the protocol overhead and the half-duplex nature of RS485.

**Q: How can I optimize performance when working with multiple slave devices?**
A: For optimal performance:
1. Use non-blocking operations with callbacks to handle responses asynchronously
2. Implement a dedicated response handling thread
3. Batch commands where possible to reduce protocol overhead
4. Use appropriate buffer sizes based on expected data volumes
5. Consider using higher baud rates for shorter cable runs
6. Implement intelligent retry logic for transient errors

## 8. Conclusion

The AI-SLDAP RS485 Driver API provides a comprehensive solution for communicating with AI-SLDAP devices over RS485, implementing the ZES protocol with key enhancements for reliability and performance.

### 8.1 API Summary

| ZES Driver API Category | API Function | Purpose | Command Series |
|-------------------------|--------------|---------|----------------|
| **Error Handle API** | `getErrorString(error)` | Provide detailed error information with transient vs. permanent categorization | N/A |
| **Master Broadcasting API** | `configureSystemSettings(commandKey, value)` | Configure system parameters via broadcasting | S-series |
| **Master Assign Data API** | `configureUserSettings(commandKey, value)` | Configure user parameters on slave devices | U-series |
| **Master Assign Data API** | `modelDataOperation(slaveAddress, address, data, isWrite, length)` | Manage AI model data in FRAM memory | W-series |
| **Master Request API** | `requestData(slaveAddress, dataKey)` | Request information from slaves with non-blocking operation | A-series |
| **Slave Response API** | `receiveSlaveResponse(slaveAddress, responseData, waitForData, timeout)` | Receive data with configurable buffer management (256KB/512KB) | N/A |

### 8.2 Key Design Features

- **Unified API Format**: Each category follows a consistent pattern, reducing learning curve
- **Protocol Abstraction**: Low-level details are handled by the driver, simplifying application code
- **Reliable Communication**: Mandatory acknowledgment mechanism replaces timeout-based detection
- **Clear Addressing Mechanism**: Explicit documentation of how U-series commands use the slave address set by S001
- **Non-Blocking Design for Airborne Environments**:
  - Critical for systems that must process multiple tasks concurrently
  - Prevents slow serial communication from blocking application threads
  - Ensures responsiveness in time-critical airborne applications
  - Implements asynchronous operation with background thread processing
- **Enhanced Error Handling**:
  - Categorized error codes (transient vs. permanent) for intelligent recovery
  - Callback mechanism for asynchronous error notification
  - Example retry logic for transient errors
- **Flexible Response Handling**:
  - Support for both callback-based (event-driven) and polling-based (sequential) approaches
  - Clear guidance on when to use each approach
  - Examples for all response handling patterns
- **Comprehensive Buffer Management**:
  - Configurable buffer sizes (256KB/512KB) to prevent data loss
  - FIFO buffer system with data ready notification
  - Advanced overflow protection with configurable policies
  - Buffer threshold callbacks for proactive management
- **Runtime Safety Checks**: Detection of multiple devices during broadcasting operations
- **Performance Optimization**:
  - Non-blocking design keeps application threads responsive
  - FIFO buffer system prevents data loss during slow serial transmission
  - Configurable buffer size accommodates different memory constraints

### 8.3 Benefits

This design addresses the specific requirements outlined in the RS485 Communication Software Protocol document, particularly:
- The need for reliable broadcast communication with acknowledgment
- Non-blocking operation for airborne environments with multiple concurrent tasks
- Comprehensive buffer management (256KB/512KB) to handle the inherently slow nature of serial communication
- Clear documentation of the U-series command addressing mechanism
- Intelligent error handling with transient vs. permanent categorization and retry logic
- Flexible response handling approaches with clear guidance on when to use each
- Runtime safety checks to prevent common issues
- FIFO buffer system with data ready notification to prevent data loss

The result is a modular, extensible, and reliable communication system that can be easily integrated into various applications while maintaining high performance even in challenging airborne environments. The non-blocking design ensures that slow serial communication does not interfere with other critical operations, while the comprehensive buffer management prevents data loss during periods of high system load. The improved error handling and response flexibility make the API more robust and user-friendly, reducing development time and improving application reliability.

## 9. Critical Design Updates Summary

### 9.1 Buffer Architecture Corrections

**Updated Buffer Specifications:**
- **Uplink Buffer**: 5 frames × 12 bytes = 60 bytes total (payload data only)
- **Downlink Buffer**: 10 frames × 12 bytes = 120 bytes total (payload data only)
- **Core Focus**: The entire protocol revolves around the **12-byte payload** which contains all meaningful communication data

**Key Insight:**
The 12-byte payload is the **heart of the entire RS485 communication protocol**. This payload contains the essential Key-Value pair data that enables all communication between PC and slave devices, including:
- System configuration commands (S-series)
- User configuration commands (U-series)
- Application data requests (A-series)
- AI model weight/bias data (W-series)

### 9.2 Buffer Flag Management Implementation

**Critical Buffer Overflow Prevention:**
- **Pre-transmission Check**: Driver checks uplink buffer flag before sending data to ensure space is available
- **Pre-storage Check**: Driver checks downlink buffer flag before storing received data to prevent overflow
- **FIFO Guarantee**: Strict First-In-First-Out ordering maintained for both PC User side and driver side
- **Overflow Handling**: Configurable policies when buffers reach capacity (discard oldest/newest or trigger error)

**Buffer Flag Structure:**
```cpp
struct BufferFlags {
    bool uplinkFull;        // Uplink buffer full flag
    bool downlinkFull;      // Downlink buffer full flag
    uint32_t uplinkUsed;    // Current uplink usage (0-5)
    uint32_t downlinkUsed;  // Current downlink usage (0-10)
};
```

### 9.3 DeviceIoControl() Implementation Strategy

**API Design Philosophy:**
- **Internal Implementation**: DeviceIoControl() is used **internally within the API functions**, not exposed directly to users
- **User-Friendly Interface**: Applications use high-level API functions (configureSystemSettings, requestData, etc.)
- **Industry Standard**: Follows standard serial port communication interface patterns
- **Abstraction Layer**: Users don't need to understand IOCTL codes or buffer management details

**Data Exchange Mechanism:**
1. **Standard Windows Interface**: Uses the industry-standard Windows driver communication method
2. **Asynchronous I/O Support**: Enables non-blocking operations with overlapped I/O
3. **Buffer Management**: Efficient data transfer through system-managed buffers
4. **Error Handling**: Comprehensive error reporting through Windows error codes
5. **FIFO Guarantee**: DeviceIoControl() calls are queued and processed in strict FIFO order

### 9.4 Payload-Centric Data Flow

**Complete Data Flow Architecture:**
1. **Transmission Path**: PC User → Buffer Flag Check → 12-byte Payload Queue → Frame Assembly → RS485 Bus
2. **Reception Path**: RS485 Bus → Frame Parsing → 12-byte Payload Extraction → Buffer Flag Check → Payload Queue → PC User
3. **Core Principle**: All meaningful data exchange occurs through the 12-byte payload, making it the heart of the protocol

**Frame Processing Pipeline:**
```
FTDI VCP Driver → Filter Driver → Frame Parser → Payload Extractor → Buffer Flag Check → Buffer Manager → User Application
     ↓              ↓              ↓              ↓                ↓                ↓              ↓
  Hardware IRQ → Work Item → DPC Context → 12-byte payload → Check downlink flag → Ring Buffer → IOCTL Completion
```

### 9.5 Enhanced Driver Architecture

**Driver-Managed Payload Buffers:**
```cpp
class RS485PayloadBuffer {
private:
    static const size_t PAYLOAD_SIZE = 12;  // Core protocol data size
    uint8_t* m_buffer;
    size_t m_capacity;      // Number of payload slots

public:
    // Core payload operations - FIFO guaranteed
    bool PushPayload(const uint8_t* payloadData);
    bool PopPayload(uint8_t* payloadData);

    // Buffer flag checking (critical for overflow prevention)
    bool CheckSpaceAvailable() const { return !IsFull(); }
    bool CheckDataAvailable() const { return !IsEmpty(); }

    // FIFO integrity verification
    bool VerifyFIFOIntegrity() const;
};
```

**Buffer Flag Manager:**
```cpp
class BufferFlagManager {
public:
    // Pre-transmission buffer flag check
    bool CheckUplinkSpaceAvailable();

    // Pre-storage buffer flag check
    bool CheckDownlinkSpaceAvailable();

    // Update flags after buffer operations
    void UpdateBufferFlags(size_t uplinkUsed, size_t downlinkUsed);
};
```

### 9.6 Implementation Summary

These critical updates ensure that:
1. **Buffer management is accurate** with correct 12-byte payload focus
2. **Overflow prevention is robust** with comprehensive flag checking
3. **FIFO ordering is guaranteed** for both PC and driver sides
4. **DeviceIoControl() usage is properly abstracted** within the API
5. **The 12-byte payload remains central** to all protocol operations

The updated design maintains the original API simplicity while providing the robust buffer management and overflow prevention mechanisms essential for reliable RS485 communication in airborne environments.

## 10. Function Code to API Category Mapping Summary

### 10.1 Complete Function Code Correspondence

The RS485 driver implements a **direct one-to-one mapping** between ZES protocol function codes and API categories, ensuring that each API call is automatically routed to the correct protocol handling mechanism:

| Function Code | Binary | Description | API Category | Buffer Check | Example Usage |
|:-------------:|:------:|:------------|:-------------|:-------------|:-------------|
| **0b111** | Assign data | **Master Broadcasting API** (S-series) | ✓ Uplink | `configureSystemSettings(0x53303031, 5)` |
| **0b111** | Assign data | **Master Assign Data API** (U/W-series) | ✓ Uplink | `configureUserSettings(0x55303031, 250)` |
| **0b110** | Request data | **Master Request API** (A-series) | ✓ Uplink | `requestData(5, 0x41303031)` |
| **0b010** | Response to Assign | **Slave Response API** (Acknowledgments) | ✓ Downlink | `receiveSlaveResponse(5, responseData)` |
| **0b001** | Response to Request | **Slave Response API** (Data responses) | ✓ Downlink | `receiveSlaveResponse(5, responseData)` |
| **0b000** | Re-send request | **Error Handle API** (Retry mechanism) | N/A | Automatic retry handling |

### 10.2 FTDI-Style Management Integration

The driver provides comprehensive management functions following industry-standard patterns:

**Port Management (Similar to FTDI FT_Open, FT_Close, FT_ListDevices):**
- `openPort()`, `closePort()`, `isPortOpen()`, `enumerateDevices()`
- `detectMultipleDevices()`, `getPortInfo()`, `getBaudRate()`

**Buffer Management (Critical for RS485 Communication):**
- `getBufferStatus()`, `checkUplinkBufferFlag()`, `checkDownlinkBufferFlag()`
- `clearBuffer()`, `setBufferOverflowPolicy()`, `getBufferCapacity()`
- `setBufferThreshold()`, `registerBufferThresholdCallback()`

**Hardware Status (Similar to FTDI FT_GetStatus):**
- `getHardwareStatus()`, `getPerformanceMetrics()`, `getLineStatus()`

### 10.3 Automatic Buffer Flag Checking

**Every data transmission operation includes mandatory buffer verification:**

1. **Before Transmission**: Uplink buffer flag checked to ensure space availability
2. **Before Storage**: Downlink buffer flag checked to prevent overflow
3. **FIFO Guarantee**: Strict First-In-First-Out ordering maintained
4. **Overflow Policies**: Configurable handling (DISCARD_OLDEST, DISCARD_NEWEST, TRIGGER_ERROR)

**Implementation Example:**
```cpp
// Automatic buffer checking in every API call
RS485Error configureSystemSettings(uint32_t commandKey, uint64_t value) {
    // Step 1: Mandatory buffer flag check
    if (!checkUplinkBufferFlag()) {
        return RS485Error::BUFFER_OVERFLOW;
    }

    // Step 2: Function code validation (0b111 for S-series)
    if (!isValidSystemCommand(commandKey)) {
        return RS485Error::INVALID_COMMAND_KEY;
    }

    // Step 3: Process with automatic function code routing
    return processAssignDataCommand(0b111, 0x00, commandKey, value);
}
```

### 10.4 Enhanced Error Management

**Comprehensive error handling with FTDI integration:**

- **FTDI Driver Errors (100-199)**: Direct mapping from FTDI VCP driver errors
- **Buffer Management Errors (150-199)**: Critical for RS485 operation
- **Protocol Errors (200-299)**: ZES-specific error handling with automatic retry
- **Function Code Errors (500-599)**: Ensures API calls match protocol requirements

**Error categorization enables intelligent recovery:**
- **Transient errors**: May succeed on retry (TIMEOUT_ERROR, CRC_ERROR, DEVICE_BUSY)
- **Permanent errors**: Require user intervention (INVALID_PARAMETER, DEVICE_NOT_FOUND)

This comprehensive design ensures that the five API categories directly correspond to the ZES protocol function codes, providing a reliable and industry-standard interface for RS485 communication while maintaining automatic buffer management and error handling.
